#!/usr/bin/env node

/**
 * Test Enhanced LangGraph Supervisor
 * 
 * Tests the new LangGraph-based autonomous agent with:
 * - Smart URL analysis for all content types
 * - Performance optimization (<90s target)
 * - Enhanced supervisor coordination
 * - 3-phase research pipeline
 */

import { EnhancedLangGraphSupervisor } from '../src/lib/agents/langgraph/enhanced-supervisor.js';

async function testEnhancedLangGraphSupervisor() {
  console.log('🚀 Testing Enhanced LangGraph Supervisor');
  console.log('=' .repeat(60));

  const testCases = [
    {
      name: 'Alternatives Test',
      goal: 'Best ChatGPT alternatives for coding',
      contentType: 'alternatives',
      expectedTime: 90000, // 90 seconds
    },
    {
      name: 'Guide Test',
      goal: 'Complete guide to setting up Next.js with TypeScript',
      contentType: 'guide',
      expectedTime: 90000,
    },
    {
      name: 'Listicle Test',
      goal: 'Top 10 AI tools for content creators',
      contentType: 'listicle',
      expectedTime: 90000,
    },
  ];

  let totalTests = 0;
  let passedTests = 0;
  let performanceTests = 0;

  for (const testCase of testCases) {
    totalTests++;
    console.log(`\n🧪 Test ${totalTests}: ${testCase.name}`);
    console.log(`📝 Goal: "${testCase.goal}"`);
    console.log(`📊 Content Type: ${testCase.contentType}`);
    console.log(`⏱️ Target Time: ${testCase.expectedTime / 1000}s`);
    
    const startTime = Date.now();
    
    try {
      // Create supervisor with performance-optimized config
      const supervisor = new EnhancedLangGraphSupervisor({
        maxRetries: 2,
        qualityThreshold: 75, // Slightly lower for speed
        timeoutMinutes: 5,    // Shorter timeout
        parallelScrapingCount: 5, // Fewer URLs for speed
        performanceTarget: 90,
        verboseLogging: false // Reduce logging for speed
      });

      console.log('🔄 Starting LangGraph execution...');
      
      const result = await supervisor.executeAutonomous(testCase.goal, {
        contentType: testCase.contentType,
        targetAudience: 'general audience',
        tone: 'professional',
        targetWordCount: 1200,
      });

      const executionTime = Date.now() - startTime;
      const executionSeconds = Math.round(executionTime / 1000);

      console.log(`\n✅ Test ${totalTests} Results:`);
      console.log(`📊 Success: ${result.success}`);
      console.log(`⏱️ Execution Time: ${executionSeconds}s`);
      console.log(`🎯 Performance Target Met: ${result.performanceMet ? 'Yes' : 'No'}`);
      console.log(`📈 Quality Score: ${result.result?.content?.qualityScore || 0}`);
      
      if (result.success) {
        passedTests++;
        console.log(`✅ Test ${totalTests} PASSED`);
        
        if (result.performanceMet) {
          performanceTests++;
          console.log(`🚀 Performance target achieved!`);
        } else {
          console.log(`⚠️ Performance target missed by ${executionSeconds - 90}s`);
        }
        
        // Display insights
        const insights = result.insights;
        console.log(`\n📊 LangGraph Insights:`);
        console.log(`  - Supervisor Decisions: ${insights.supervisorDecisions}`);
        console.log(`  - Quality Gates: ${insights.qualityGates}`);
        console.log(`  - URLs Scraped: ${insights.dataMetrics?.urlsScraped || 0}`);
        console.log(`  - Analysis Depth: ${insights.dataMetrics?.analysisDepth || 0}`);
        console.log(`  - Queries Generated: ${insights.dataMetrics?.queriesGenerated || 0}`);
        console.log(`  - Phases Completed: ${insights.phasesCompleted?.length || 0}`);
        
      } else {
        console.log(`❌ Test ${totalTests} FAILED: ${result.error}`);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const executionSeconds = Math.round(executionTime / 1000);
      
      console.log(`❌ Test ${totalTests} ERROR after ${executionSeconds}s:`);
      console.log(`   ${error.message}`);
    }
    
    // Add delay between tests
    if (totalTests < testCases.length) {
      console.log('\n⏸️ Waiting 2 seconds before next test...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  console.log('\n' + '=' .repeat(60));
  console.log('📊 ENHANCED LANGGRAPH SUPERVISOR TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`✅ Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`🚀 Performance Tests: ${performanceTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  console.log(`⚡ Performance Rate: ${Math.round((performanceTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 ALL TESTS PASSED! Enhanced LangGraph Supervisor is working correctly.');
  } else {
    console.log(`\n⚠️ ${totalTests - passedTests} test(s) failed. Check the logs above.`);
  }

  if (performanceTests === totalTests) {
    console.log('🚀 ALL PERFORMANCE TARGETS MET! System is optimized.');
  } else {
    console.log(`⏱️ ${totalTests - performanceTests} test(s) exceeded performance target.`);
  }

  console.log('\n🔧 Features Tested:');
  console.log('  ✅ LangGraph state management');
  console.log('  ✅ Smart URL analysis (universal content types)');
  console.log('  ✅ 3-phase research pipeline');
  console.log('  ✅ Performance optimization');
  console.log('  ✅ Supervisor coordination');
  console.log('  ✅ Enhanced error handling');
  console.log('  ✅ Quality gates and metrics');
  
  console.log('\n🎯 Next Steps:');
  console.log('  1. Deploy to production environment');
  console.log('  2. Monitor performance in real-world usage');
  console.log('  3. Fine-tune quality thresholds based on results');
  console.log('  4. Add more content type specializations');
}

// Run the test
testEnhancedLangGraphSupervisor().catch(error => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});