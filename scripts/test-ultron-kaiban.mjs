#!/usr/bin/env node

/**
 * Test script for KaibanJS Ultron Agent System
 * Demonstrates the multi-agent YouTube script generation workflow
 */

import dotenv from 'dotenv';
import fetch from 'node-fetch';

// Load environment variables
dotenv.config();

const API_BASE = 'http://localhost:3000';

async function testUltronAPI() {
  console.log('🚀 Testing KaibanJS Ultron Agent System');
  console.log('=' .repeat(50));

  try {
    // Test 1: Get team information
    console.log('\n📋 Test 1: Getting team information...');
    const infoResponse = await fetch(`${API_BASE}/api/ultron/kaiban?team=standard`);
    const infoData = await infoResponse.json();
    
    if (infoData.success) {
      console.log('✅ Team info retrieved successfully');
      console.log('📊 Team:', infoData.teamInfo.name);
      console.log('🤖 Agents:', infoData.teamInfo.agents.join(', '));
      console.log('📝 Tasks:', infoData.teamInfo.tasks.join(', '));
    } else {
      console.log('❌ Failed to get team info');
    }

    // Test 2: Generate a single script (Quick team for faster testing)
    console.log('\n📝 Test 2: Generating YouTube script...');
    const scriptConfig = {
      action: 'generate',
      topic: 'How to learn JavaScript in 2024',
      targetAudience: 'Beginner programmers',
      scriptStyle: 'educational',
      videoLength: 8,
      includeHooks: true,
      includeCallToAction: true,
      teamType: 'quick' // Using quick team for faster testing
    };

    console.log('⚙️ Configuration:', JSON.stringify(scriptConfig, null, 2));

    const scriptResponse = await fetch(`${API_BASE}/api/ultron/kaiban`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(scriptConfig)
    });

    const scriptData = await scriptResponse.json();
    
    if (scriptData.success) {
      console.log('✅ Script generated successfully!');
      console.log('\n📜 Generated Script:');
      console.log('-'.repeat(50));
      console.log(scriptData.script);
      console.log('-'.repeat(50));
      
      if (scriptData.metadata) {
        console.log('\n📊 Metadata:');
        console.log('🎯 Topic:', scriptData.metadata.topic);
        console.log('👥 Audience:', scriptData.metadata.targetAudience);
        console.log('🎨 Style:', scriptData.metadata.scriptStyle);
        console.log('⏱️ Length:', scriptData.metadata.videoLength, 'minutes');
        console.log('🕒 Generated:', scriptData.metadata.timestamp);
      }
    } else {
      console.log('❌ Script generation failed:', scriptData.error);
    }

    // Test 3: Get advanced team info
    console.log('\n🔬 Test 3: Getting advanced team information...');
    const advancedInfoResponse = await fetch(`${API_BASE}/api/ultron/kaiban?team=advanced`);
    const advancedInfoData = await advancedInfoResponse.json();
    
    if (advancedInfoData.success) {
      console.log('✅ Advanced team info retrieved');
      console.log('📊 Team:', advancedInfoData.teamInfo.name);
      console.log('🤖 Agents:', advancedInfoData.teamInfo.agents.length);
      console.log('📝 Tasks:', advancedInfoData.teamInfo.tasks.length);
    }

    // Test 4: Batch generation (small test)
    console.log('\n🔄 Test 4: Testing batch generation...');
    const batchConfig = {
      action: 'batch',
      topics: [
        'Python vs JavaScript for beginners',
        'Top 5 React hooks explained'
      ],
      targetAudience: 'Web developers',
      scriptStyle: 'tutorial',
      videoLength: 5,
      teamType: 'quick'
    };

    const batchResponse = await fetch(`${API_BASE}/api/ultron/kaiban`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(batchConfig)
    });

    const batchData = await batchResponse.json();
    
    if (batchData.success) {
      console.log('✅ Batch generation completed!');
      console.log('📊 Summary:');
      console.log('  📝 Total:', batchData.summary.total);
      console.log('  ✅ Successful:', batchData.summary.successful);
      console.log('  ❌ Failed:', batchData.summary.failed);
      
      batchData.results.forEach((result, index) => {
        console.log(`\n📜 Script ${index + 1}: ${result.topic}`);
        console.log(`   Status: ${result.success ? '✅ Success' : '❌ Failed'}`);
        if (result.success && result.script) {
          console.log(`   Length: ${result.script.length} characters`);
        }
      });
    } else {
      console.log('❌ Batch generation failed');
    }

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

async function testAgentWorkflow() {
  console.log('\n🔍 Testing Individual Agent Workflow');
  console.log('=' .repeat(50));

  const workflowSteps = [
    '🔍 Web Search Agent: Researching topic with Tavily API',
    '📺 Caption Analyser: Analyzing YouTube video patterns',
    '👤 Supervisor Agent: Synthesizing research and providing guidance', 
    '✍️ Writer Agent: Generating script based on guidance',
    '✅ Quality Assurance: Reviewing and optimizing script'
  ];

  for (let i = 0; i < workflowSteps.length; i++) {
    console.log(`${i + 1}. ${workflowSteps[i]}`);
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 Workflow simulation completed!');
}

async function showSystemCapabilities() {
  console.log('\n🌟 KaibanJS Ultron Agent System Capabilities');
  console.log('=' .repeat(50));

  const capabilities = [
    {
      agent: '🔍 Web Search Agent',
      description: 'Uses Tavily API for comprehensive topic research',
      features: [
        'Multi-strategy search approach',
        'Source credibility assessment', 
        'Trend analysis and competitive insights',
        'Audience-specific content discovery'
      ]
    },
    {
      agent: '📺 Caption Analyser Agent',
      description: 'Analyzes YouTube videos for script patterns',
      features: [
        'Hook pattern identification',
        'Engagement technique analysis',
        'Writing style breakdown',
        'Psychological trigger assessment'
      ]
    },
    {
      agent: '👤 Supervisor Agent',
      description: 'Orchestrates workflow and ensures quality',
      features: [
        'Research synthesis and prioritization',
        'Strategic guidance formulation',
        'Quality control framework',
        'Performance prediction'
      ]
    },
    {
      agent: '✍️ Writer Agent',
      description: 'Generates human-like YouTube scripts',
      features: [
        'Advanced copywriting techniques',
        'Psychological engagement optimization',
        'Audience-specific tone adaptation',
        'SEO and retention optimization'
      ]
    }
  ];

  capabilities.forEach(({ agent, description, features }) => {
    console.log(`\n${agent}`);
    console.log(`   ${description}`);
    features.forEach(feature => {
      console.log(`   • ${feature}`);
    });
  });

  console.log('\n🔧 Team Configurations:');
  console.log('   • Standard: Full workflow with all agents');
  console.log('   • Advanced: Enhanced analysis and premium writing');
  console.log('   • Quick: Streamlined for rapid content creation');

  console.log('\n🎯 Supported Features:');
  console.log('   • Single script generation');
  console.log('   • Batch processing for multiple topics');
  console.log('   • Multiple script styles (educational, entertainment, etc.)');
  console.log('   • Audience targeting and customization');
  console.log('   • Integration with Gemini AI models');
}

// Main execution
async function main() {
  console.log('🤖 KaibanJS Ultron Agent System - Test Suite');
  console.log('⚡ Powered by Multi-Agent AI Collaboration');
  console.log('🔗 Using Tavily, YouTube, and Gemini APIs');
  
  await showSystemCapabilities();
  await testAgentWorkflow();
  await testUltronAPI();
  
  console.log('\n🎉 All tests completed!');
  console.log('\n💡 Next Steps:');
  console.log('   1. Set up API keys (TAVILY_API_KEY, GEMINI_API_KEY)');
  console.log('   2. Start the development server: npm run dev');
  console.log('   3. Test the API at: http://localhost:3000/api/ultron/kaiban');
  console.log('   4. Create amazing YouTube scripts with AI agents! 🚀');
}

main().catch(console.error); 