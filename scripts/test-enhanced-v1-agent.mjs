#!/usr/bin/env node

/**
 * Enhanced V1 Agent Test Script
 * Tests the improved Invincible V1 agent with enhanced content generation,
 * structure validation, and word count enforcement.
 */

import fetch from 'node-fetch';
import fs from 'fs';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

// Enhanced test configuration
const ENHANCED_TEST_CONFIG = {
  topic: 'The Ultimate Guide to Building High-Performance React Applications in 2025',
  customInstructions: 'Focus on practical, actionable strategies with real-world examples. Include performance metrics, best practices, and modern React patterns. Ensure comprehensive coverage with detailed explanations.',
  targetAudience: 'React developers and frontend engineers',
  contentLength: 3000, // Increased target for testing
  tone: 'professional yet engaging',
  keywords: ['React performance', 'optimization', 'rendering', 'state management', 'best practices'],
  searchDepth: 7,
  competitorCount: 10,
  deepSearchQueriesPerTopic: 15,
  uniquenessLevel: 'high'
};

// Test scenarios for validation
const TEST_SCENARIOS = [
  {
    name: 'Short Content Test',
    config: { ...ENHANCED_TEST_CONFIG, contentLength: 1500 },
    expectedMinWords: 1455, // 3% tolerance
    expectedMaxWords: 1545
  },
  {
    name: 'Medium Content Test', 
    config: { ...ENHANCED_TEST_CONFIG, contentLength: 2500 },
    expectedMinWords: 2425,
    expectedMaxWords: 2575
  },
  {
    name: 'Long Content Test',
    config: { ...ENHANCED_TEST_CONFIG, contentLength: 4000 },
    expectedMinWords: 3880,
    expectedMaxWords: 4120
  }
];

function countWords(text) {
  return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

function validateStructure(content) {
  const validation = {
    hasTitle: content.includes('# '),
    sectionCount: (content.match(/^##/gm) || []).length,
    hasConclusion: content.toLowerCase().includes('conclusion') || 
                   content.toLowerCase().includes('summary') ||
                   content.toLowerCase().includes('takeaway'),
    hasProperEnding: !content.endsWith('...'),
    paragraphCount: content.split(/\n\n+/).filter(p => 
      !p.startsWith('#') && p.trim().length > 0
    ).length
  };
  
  // Check paragraph lengths
  const paragraphs = content.split(/\n\n+/).filter(p => 
    !p.startsWith('#') && p.trim().length > 0
  );
  
  validation.shortParagraphs = paragraphs.filter(p => 
    p.trim().split(/\s+/).length < 80
  ).length;
  
  validation.averageParagraphLength = paragraphs.length > 0 
    ? paragraphs.reduce((sum, p) => sum + p.trim().split(/\s+/).length, 0) / paragraphs.length
    : 0;
  
  return validation;
}

function calculateCompletionScore(content, targetWordCount) {
  const wordCount = countWords(content);
  const structure = validateStructure(content);
  
  // Word count score (strict 3% tolerance)
  const tolerance = 0.03;
  const minWords = targetWordCount * (1 - tolerance);
  const maxWords = targetWordCount * (1 + tolerance);
  
  let wordCountScore = 0;
  if (wordCount >= minWords && wordCount <= maxWords) {
    wordCountScore = 100;
  } else if (wordCount >= targetWordCount * 0.8) {
    wordCountScore = Math.min(90, (wordCount / targetWordCount) * 100);
  } else {
    wordCountScore = Math.min(70, (wordCount / targetWordCount) * 100);
  }
  
  // Structure score
  let structureScore = 0;
  if (structure.hasTitle) structureScore += 20;
  if (structure.sectionCount >= 6) structureScore += 30;
  if (structure.hasConclusion) structureScore += 20;
  if (structure.hasProperEnding) structureScore += 15;
  if (structure.shortParagraphs <= 2) structureScore += 15;
  
  // Paragraph score
  const paragraphScore = Math.max(0, 100 - (structure.shortParagraphs * 10));
  
  // Overall score
  const overallScore = Math.round(
    (wordCountScore * 0.5) + 
    (structureScore * 0.3) + 
    (paragraphScore * 0.2)
  );
  
  return {
    wordCountScore: Math.round(wordCountScore),
    structureScore,
    paragraphScore: Math.round(paragraphScore),
    overallScore,
    details: structure
  };
}

async function testEnhancedV1Agent() {
  console.log('🚀 Testing Enhanced Invincible V1 Agent');
  console.log('=' .repeat(60));
  console.log('');

  try {
    // First, check agent status
    console.log('🔍 Checking enhanced agent status...');
    const statusResponse = await fetch(`${API_BASE_URL}/api/invincible`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    if (!statusResponse.ok) {
      throw new Error(`Status check failed: ${statusResponse.status}`);
    }

    const status = await statusResponse.json();
    console.log('✅ Agent Status:', status.status);
    console.log('📋 Capabilities:', status.capabilities?.length || 0, 'features');
    console.log('');

    // Run test scenarios
    for (let i = 0; i < TEST_SCENARIOS.length; i++) {
      const scenario = TEST_SCENARIOS[i];
      console.log(`\n🧪 Running ${scenario.name} (${i + 1}/${TEST_SCENARIOS.length})`);
      console.log('-'.repeat(40));
      console.log(`📝 Target: ${scenario.config.contentLength} words`);
      console.log(`📏 Expected range: ${scenario.expectedMinWords}-${scenario.expectedMaxWords} words`);
      console.log('⏳ This may take 2-5 minutes...');
      
      const startTime = Date.now();
      
      const response = await fetch(`${API_BASE_URL}/api/invincible`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(scenario.config),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error(`❌ ${scenario.name} failed:`, error.error || response.status);
        continue;
      }

      const result = await response.json();
      const executionTime = Date.now() - startTime;
      
      if (!result.success) {
        console.error(`❌ ${scenario.name} failed:`, result.error);
        continue;
      }

      // Analyze results
      const article = result.article;
      const wordCount = countWords(article.content);
      const structure = validateStructure(article.content);
      const completionScore = calculateCompletionScore(article.content, scenario.config.contentLength);
      
      console.log('\n📊 RESULTS:');
      console.log(`   Execution Time: ${Math.round(executionTime / 1000)}s`);
      console.log(`   Word Count: ${wordCount} (target: ${scenario.config.contentLength})`);
      console.log(`   Word Count Achievement: ${wordCount >= scenario.expectedMinWords && wordCount <= scenario.expectedMaxWords ? '✅ PASS' : '❌ FAIL'}`);
      console.log(`   Title: ${article.title}`);
      console.log('\n📋 STRUCTURE ANALYSIS:');
      console.log(`   Main Title: ${structure.hasTitle ? '✅' : '❌'}`);
      console.log(`   Sections: ${structure.sectionCount} (requirement: ≥6) ${structure.sectionCount >= 6 ? '✅' : '❌'}`);
      console.log(`   Conclusion: ${structure.hasConclusion ? '✅' : '❌'}`);
      console.log(`   Proper Ending: ${structure.hasProperEnding ? '✅' : '❌'}`);
      console.log(`   Total Paragraphs: ${structure.paragraphCount}`);
      console.log(`   Short Paragraphs: ${structure.shortParagraphs} ${structure.shortParagraphs <= 2 ? '✅' : '❌'}`);
      console.log(`   Avg Paragraph Length: ${Math.round(structure.averageParagraphLength)} words`);
      
      console.log('\n🎯 COMPLETION SCORES:');
      console.log(`   Word Count Score: ${completionScore.wordCountScore}%`);
      console.log(`   Structure Score: ${completionScore.structureScore}%`);
      console.log(`   Paragraph Score: ${completionScore.paragraphScore}%`);
      console.log(`   Overall Score: ${completionScore.overallScore}% ${completionScore.overallScore >= 85 ? '✅ EXCELLENT' : completionScore.overallScore >= 70 ? '⚠️ GOOD' : '❌ NEEDS IMPROVEMENT'}`);

      // Save results
      const resultFile = `enhanced-v1-test-${scenario.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.json`;
      fs.writeFileSync(resultFile, JSON.stringify({
        scenario: scenario.name,
        config: scenario.config,
        result: {
          title: article.title,
          wordCount,
          targetWordCount: scenario.config.contentLength,
          structure,
          completionScore,
          executionTime,
          content: article.content.substring(0, 1000) + '...' // Preview only
        }
      }, null, 2));
      
      console.log(`💾 Results saved to: ${resultFile}`);
      
      // Brief pause between tests
      if (i < TEST_SCENARIOS.length - 1) {
        console.log('\n⏳ Waiting 30 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
    }

    console.log('\n🎉 Enhanced V1 Agent Testing Complete!');
    console.log('=' .repeat(60));

  } catch (error) {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  }
}

// Additional test for structure validation
async function testStructureValidation() {
  console.log('\n🔧 Testing Structure Validation...');
  
  const testContent = `# Test Article

## Introduction
This is a short intro.

## Section 1
This is a very short paragraph that should be flagged.

## Section 2
This is a properly sized paragraph that contains enough content to meet the minimum word count requirement. It should have sufficient detail and explanation to provide value to readers while maintaining readability and engagement throughout the entire section.

## Section 3
Another short one.

## Conclusion
This article demonstrates the structure validation system in action.`;

  const structure = validateStructure(testContent);
  const score = calculateCompletionScore(testContent, 500);
  
  console.log('📋 Test Content Structure:');
  console.log(`   Sections: ${structure.sectionCount}`);
  console.log(`   Short Paragraphs: ${structure.shortParagraphs}`);
  console.log(`   Avg Paragraph Length: ${Math.round(structure.averageParagraphLength)} words`);
  console.log(`   Completion Score: ${score.overallScore}%`);
}

// Run tests
console.log('🚀 Enhanced Invincible V1 Agent - Comprehensive Test Suite');
console.log('Testing improved content generation, structure validation, and word count enforcement');
console.log('');

await testStructureValidation();
await testEnhancedV1Agent(); 