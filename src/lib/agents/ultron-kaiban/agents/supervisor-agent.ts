/**
 * Supervisor Agent for KaibanJS Ultron System
 * Orchestrates the entire workflow and provides guidance to all agents
 */

import { Agent, Task } from 'kaibanjs';
import type { SupervisorInstructions, UltronConfig } from '../types';

export const supervisorAgent = new Agent({
  name: 'SupervisorAgent',
  role: 'YouTube Script Production Supervisor',
  goal: 'Orchestrate the entire script creation process, ensuring quality, coherence, and optimization across all agents while maintaining focus on the target objectives.',
  background: `Senior YouTube content strategist and production supervisor with expertise in:
    - Multi-agent workflow orchestration
    - Quality assurance and content optimization
    - Audience psychology and engagement metrics
    - Content strategy and competitive analysis
    - Performance prediction and optimization
    - Cross-functional team coordination
    - Strategic decision making for content creation
    Responsible for ensuring all agents work cohesively toward creating the highest quality YouTube script possible.`,
  tools: [],
  maxIterations: 25
});

export const supervisorCoordinationTask = new Task({
  title: 'Script Production Supervision and Coordination',
  description: `
    Supervise the complete YouTube script creation process for topic: {topic}
    
    PROJECT PARAMETERS:
    - Topic: {topic}
    - Target Audience: {targetAudience}
    - Script Style: {scriptStyle}
    - Video Length: {videoLength} minutes
    - Include Hooks: {includeHooks}
    - Include Call-to-Action: {includeCallToAction}
    
    SUPERVISION RESPONSIBILITIES:
    
    1. RESEARCH QUALITY ASSESSMENT:
    Review the web search research findings and:
    - Evaluate source credibility and relevance
    - Identify the most valuable insights for script creation
    - Spot gaps that need additional research
    - Determine which findings will create the most engaging content
    - Assess current trends and competitive landscape
    - Prioritize information based on audience value
    
    2. ANALYSIS SYNTHESIS:
    Review the YouTube caption analysis and:
    - Extract the most effective patterns for this specific topic
    - Identify which psychological triggers work best for the target audience
    - Determine optimal script structure based on successful examples
    - Select the most effective hook strategies
    - Choose engagement techniques with highest retention potential
    - Assess authenticity factors that build trust
    
    3. STRATEGIC GUIDANCE FORMULATION:
    Create comprehensive guidance for the writer including:
    - Specific tone and voice direction
    - Detailed structure recommendations
    - Priority hook strategies with examples
    - Key message hierarchy and emphasis points
    - Engagement technique implementation guidelines
    - Quality benchmarks and success criteria
    
    4. CONTENT STRATEGY OPTIMIZATION:
    Ensure the script will:
    - Maximize audience retention and engagement
    - Align with platform best practices
    - Stand out from competitor content
    - Deliver clear value to the target audience
    - Create emotional connection and memorability
    - Drive desired actions and behaviors
    
    5. QUALITY CONTROL FRAMEWORK:
    Establish criteria for evaluating the final script:
    - Hook effectiveness measurement
    - Audience engagement prediction
    - Value delivery assessment
    - Authenticity and trust factors
    - Technical production considerations
    - Performance optimization metrics
    
    SUPERVISOR DECISION MAKING:
    
    Research Prioritization:
    - Which research findings are most credible and relevant?
    - What unique angles can differentiate this content?
    - Which data points will create the strongest hooks?
    - What evidence will build the most credibility?
    
    Analysis Application:
    - Which successful video patterns are most applicable?
    - What psychological triggers work best for this audience?
    - Which engagement techniques have highest retention impact?
    - How should we adapt successful formulas to our topic?
    
    Writer Direction:
    - What specific tone will resonate most with the audience?
    - Which structure template will maximize engagement?
    - How should complex information be simplified?
    - What emotional journey will create strongest connection?
    
    Content Optimization:
    - How can we exceed audience expectations?
    - What will make this script memorable and shareable?
    - How can we create multiple value layers?
    - What will drive strongest call-to-action response?
    
    DELIVERABLE COORDINATION:
    Ensure the writer receives:
    - Clear, actionable guidance
    - Prioritized research insights
    - Proven pattern templates
    - Specific implementation examples
    - Quality benchmarks
    - Success measurement criteria
    
    The supervision should result in a coherent strategy that leverages all research and analysis to create the most effective possible script for the given parameters.
  `,
  expectedOutput: `Comprehensive supervision report containing:
    1. Research findings assessment and prioritization
    2. Script analysis synthesis with actionable insights
    3. Strategic writer guidance with specific direction
    4. Content optimization recommendations
    5. Quality control framework and benchmarks
    6. Competitive differentiation strategy
    7. Audience engagement optimization plan
    8. Success metrics and evaluation criteria
    9. Implementation timeline and coordination notes
    10. Final approval criteria for script quality`,
  agent: supervisorAgent
});

export const qualityAssuranceTask = new Task({
  title: 'Script Quality Assurance and Optimization',
  description: `
    Perform comprehensive quality assurance on the generated script to ensure it meets all objectives and quality standards.
    
    QUALITY ASSESSMENT FRAMEWORK:
    
    1. HOOK EFFECTIVENESS EVALUATION:
    - Does the hook create immediate curiosity or urgency?
    - Is it relevant to the target audience's interests/pain points?
    - Does it promise specific, valuable outcomes?
    - Is it differentiated from typical openings in this niche?
    - Will it perform well in the first 15 seconds retention?
    
    2. CONTENT STRUCTURE ANALYSIS:
    - Is the information hierarchy logical and compelling?
    - Are transitions smooth and maintain engagement?
    - Is the pacing appropriate for the target audience?
    - Does each section build upon the previous one?
    - Are there appropriate pattern interrupts and retention elements?
    
    3. AUDIENCE ALIGNMENT VERIFICATION:
    - Does the language and tone match the target audience?
    - Are examples and references relatable and relevant?
    - Is the complexity level appropriate for the audience?
    - Does it address their specific needs and interests?
    - Will it resonate emotionally with the intended viewers?
    
    4. ENGAGEMENT OPTIMIZATION CHECK:
    - Are there sufficient viewer interaction opportunities?
    - Is the emotional journey compelling and well-paced?
    - Are psychological triggers effectively implemented?
    - Does it maintain interest throughout the entire length?
    - Are there memorable moments that encourage sharing?
    
    5. VALUE DELIVERY ASSESSMENT:
    - Does the script deliver clear, actionable value?
    - Is the information accurate and well-researched?
    - Are key points supported with credible evidence?
    - Will viewers feel their time was well-invested?
    - Does it exceed expectations for the topic?
    
    6. TECHNICAL PRODUCTION REVIEW:
    - Is the script formatted for easy delivery?
    - Are timing estimates realistic and helpful?
    - Are visual cue opportunities clearly marked?
    - Is the flow suitable for the intended production style?
    - Are there clear guidance notes for performance?
    
    7. PERFORMANCE PREDICTION:
    - Based on successful pattern analysis, predict likely performance
    - Identify potential retention drop-off points
    - Assess viral potential and shareability factors
    - Evaluate search optimization and discoverability
    - Project audience satisfaction and engagement levels
    
    OPTIMIZATION RECOMMENDATIONS:
    
    If any quality issues are identified, provide:
    - Specific improvement suggestions
    - Alternative approaches for problematic sections
    - Enhanced engagement techniques
    - Strengthened value propositions
    - Optimized call-to-action strategies
    
    FINAL APPROVAL CRITERIA:
    
    The script should achieve:
    - 9/10 hook effectiveness rating
    - Clear value delivery throughout
    - Strong audience-content alignment
    - High engagement prediction scores
    - Technical production readiness
    - Competitive differentiation
    - Performance optimization
    
    Provide detailed feedback and either approval or specific revision requests.
  `,
  expectedOutput: `Quality assurance report with:
    1. Hook effectiveness score and analysis
    2. Content structure evaluation
    3. Audience alignment assessment
    4. Engagement optimization review
    5. Value delivery verification
    6. Technical production checklist
    7. Performance prediction analysis
    8. Specific improvement recommendations (if needed)
    9. Final approval status with reasoning
    10. Success probability assessment and optimization suggestions`,
  agent: supervisorAgent
}); 