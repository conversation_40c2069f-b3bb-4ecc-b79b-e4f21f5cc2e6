/**
 * Web Search Agent for KaibanJS Ultron System
 * Uses Tavily API for comprehensive topic research
 */

import { Agent, Task } from 'kaibanjs';
import type { WebSearchResult } from '../types';

// Custom Tavily search function
async function searchWithTavily(query: string): Promise<WebSearchResult[]> {
  try {
    const response = await fetch('https://api.tavily.com/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.TAVILY_API_KEY}`
      },
      body: JSON.stringify({
        query,
        max_results: 10,
        search_depth: 'advanced',
        include_answer: true,
        include_raw_content: true
      })
    });

    if (!response.ok) {
      throw new Error(`Tavily API error: ${response.status}`);
    }

    const data = await response.json();
    
    return data.results?.map((result: any) => ({
      url: result.url,
      title: result.title,
      content: result.content || result.raw_content || '',
      relevanceScore: result.score || 0.5,
      publishDate: result.published_date,
      domain: new URL(result.url).hostname
    })) || [];
  } catch (error) {
    console.error('Tavily search error:', error);
    return [];
  }
}

export const webSearchAgent = new Agent({
  name: 'ResearcherAgent',
  role: 'Web Research Specialist',
  goal: 'Conduct comprehensive research on given topics using web search to gather current, relevant, and authoritative information.',
  background: `Expert in information gathering, fact-checking, and content research. 
    Skilled at identifying credible sources, extracting key insights, and organizing research findings.
    Experienced in evaluating source reliability and relevance to specific topics.
    Has access to advanced web search capabilities through Tavily API.`,
  tools: [],
  maxIterations: 10
});

export const webSearchTask = new Task({
  title: 'Comprehensive Topic Research',
  description: `
    Research the topic: {topic} thoroughly using web search.
    
    Your research should focus on:
    1. Current trends and developments related to {topic}
    2. Key facts, statistics, and data points
    3. Expert opinions and authoritative sources
    4. Recent news and updates (within last 6 months)
    5. Different perspectives and viewpoints on the topic
    6. Practical examples and case studies
    7. Common misconceptions or debates around the topic
    
    For each search result, evaluate:
    - Credibility and authority of the source
    - Relevance to the topic
    - Recency of the information
    - Unique insights or perspectives offered
    
    Organize your findings into:
    - Key facts and statistics
    - Recent developments
    - Expert insights
    - Practical examples
    - Different viewpoints
    - Source credibility assessment
    
    Target audience consideration: {targetAudience}
    Script style: {scriptStyle}
    
    Provide comprehensive research that will help create an engaging, accurate, and informative video script.
  `,
  expectedOutput: `Detailed research report containing:
    1. Key findings organized by category
    2. Source credibility ratings
    3. Most relevant and current information
    4. Different perspectives on the topic
    5. Practical examples and data points
    6. Recommended focus areas for script writing`,
  agent: webSearchAgent,
});

// Enhanced research task with multiple search strategies
export const advancedWebSearchTask = new Task({
  title: 'Multi-Strategy Topic Research',
  description: `
    Conduct advanced research on: {topic} using multiple search strategies.
    
    Search Strategy 1: Current Trends
    - Search for "{topic} 2024 trends"
    - Search for "{topic} latest news"
    - Search for "{topic} recent developments"
    
    Search Strategy 2: Expert Insights
    - Search for "{topic} expert opinion"
    - Search for "{topic} analysis"
    - Search for "{topic} research study"
    
    Search Strategy 3: Practical Information
    - Search for "{topic} how to"
    - Search for "{topic} guide"
    - Search for "{topic} examples"
    
    Search Strategy 4: Audience-Specific
    - Search for "{topic} for {targetAudience}"
    - Search for "{topic} beginner guide" (if applicable)
    
    For each search, analyze:
    1. Source authority and credibility
    2. Information recency and relevance
    3. Unique insights and perspectives
    4. Practical applicability
    5. Audience appropriateness
    
    Synthesize findings into:
    - Core concepts and definitions
    - Current state and trends
    - Expert perspectives
    - Practical examples
    - Common questions and concerns
    - Controversial or debated aspects
    
    Prioritize information that would make for engaging video content.
  `,
  expectedOutput: `Comprehensive research synthesis with:
    1. Executive summary of key findings
    2. Categorized information (trends, expert insights, practical info)
    3. Source quality assessment
    4. Content recommendations for video script
    5. Potential hooks and engaging angles
    6. Fact-check worthy claims and statistics`,
  agent: webSearchAgent,
}); 