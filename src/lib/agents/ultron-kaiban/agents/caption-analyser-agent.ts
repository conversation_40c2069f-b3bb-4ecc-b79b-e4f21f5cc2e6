/**
 * Caption Analyser Agent for KaibanJS Ultron System
 * Searches YouTube videos and analyzes captions for script patterns
 */

import { Agent, Task } from 'kaibanjs';
import type { YouTubeVideoData, CaptionAnalysis } from '../types';

// YouTube search and caption extraction using your existing service
async function searchYouTubeVideos(topic: string): Promise<YouTubeVideoData[]> {
  try {
    // Using your existing YouTube service
    const response = await fetch('/api/search/youtube', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        query: topic,
        maxResults: 5,
        type: 'video'
      })
    });

    if (!response.ok) {
      throw new Error(`YouTube search error: ${response.status}`);
    }

    const data = await response.json();
    return data.videos || [];
  } catch (error) {
    console.error('YouTube search error:', error);
    return [];
  }
}

// Extract captions using your existing caption service
async function extractCaptions(videoId: string): Promise<string> {
  try {
    const response = await fetch('/api/extract/captions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ videoId })
    });

    if (!response.ok) {
      throw new Error(`Caption extraction error: ${response.status}`);
    }

    const data = await response.json();
    return data.transcript || '';
  } catch (error) {
    console.error('Caption extraction error:', error);
    return '';
  }
}

export const captionAnalyserAgent = new Agent({
  name: 'CaptionAnalyserAgent',
  role: 'YouTube Script Pattern Analyst',
  goal: 'Analyze YouTube video captions to identify successful script patterns, hooks, and writing techniques for the given topic.',
  background: `Expert in YouTube content analysis, script structure evaluation, and video engagement patterns.
    Specialized in identifying what makes scripts successful, including:
    - Hook techniques and attention-grabbing openings
    - Narrative structure and pacing
    - Audience engagement tactics
    - Writing style and tone analysis
    - Information delivery patterns
    - Call-to-action placement and effectiveness
    Has access to YouTube search and caption extraction capabilities.`,
  tools: [],
  maxIterations: 15
});

export const captionAnalysisTask = new Task({
  title: 'YouTube Script Pattern Analysis',
  description: `
    Search for YouTube videos about: {topic} and analyze their script patterns.
    
    Step 1: YouTube Video Discovery
    - Search for top-performing videos related to {topic}
    - Focus on videos with high engagement (views, likes, comments)
    - Target videos from the last 12 months for current trends
    - Look for videos that match the target audience: {targetAudience}
    - Consider the desired script style: {scriptStyle}
    
    Step 2: Caption Extraction and Analysis
    For each video found, analyze the script structure:
    
    Opening Hook Analysis:
    - How does the video grab attention in the first 10 seconds?
    - What questions, statements, or promises are made?
    - How does it create curiosity or urgency?
    
    Writing Style Analysis:
    - Tone (conversational, formal, enthusiastic, educational)
    - Vocabulary level and complexity
    - Sentence structure (short/punchy vs long/detailed)
    - Use of rhetorical questions
    - Personal pronouns usage (you, we, I)
    
    Content Structure Analysis:
    - How is the video organized? (intro -> points -> conclusion)
    - How many main points are covered?
    - How are transitions handled between sections?
    - How long is each section proportionally?
    
    Engagement Techniques:
    - What questions are asked to viewers?
    - How often are viewers directly addressed?
    - What pattern interrupts or attention refreshers are used?
    - How is suspense or curiosity maintained?
    
    Narration Style:
    - Is it first person, second person, or third person?
    - Energy level and pacing
    - Use of examples and analogies
    - How complex topics are simplified
    
    Step 3: Pattern Identification
    Identify common patterns across successful videos:
    - Most effective hook formats for this topic
    - Optimal script structure for audience retention
    - Key phrases and language patterns that work
    - Timing and pacing preferences
    - Common engagement techniques
    
    Step 4: Recommendations
    Based on analysis, provide specific recommendations for:
    - Best hook strategies for {topic}
    - Optimal writing tone and style
    - Recommended script structure
    - Key engagement techniques to include
    - Phrases and language patterns to adopt
    - What to avoid based on less successful examples
    
    Target the analysis for: {targetAudience}
    Script style preference: {scriptStyle}
  `,
  expectedOutput: `Comprehensive script analysis report containing:
    1. Video analysis summary (5-7 videos analyzed)
    2. Hook pattern analysis with specific examples
    3. Writing style breakdown with recommendations
    4. Content structure templates that work
    5. Engagement technique catalog
    6. Narration style guide
    7. Specific recommendations for the topic
    8. Language patterns and key phrases to use
    9. Common mistakes to avoid
    10. Success metrics and benchmarks`,
  agent: captionAnalyserAgent
});

export const advancedCaptionAnalysisTask = new Task({
  title: 'Deep Script Psychology Analysis',
  description: `
    Perform advanced psychological analysis of successful YouTube scripts for: {topic}
    
    Advanced Analysis Areas:
    
    1. Psychological Hooks:
    - Fear-based hooks (what viewers want to avoid)
    - Desire-based hooks (what viewers want to achieve)
    - Curiosity gaps (incomplete information that creates intrigue)
    - Social proof hooks (popularity, expert endorsement)
    - Urgency hooks (time-sensitive information)
    
    2. Cognitive Load Management:
    - How complex information is broken down
    - Use of analogies and metaphors
    - Visual language and descriptive techniques
    - Information hierarchy and prioritization
    
    3. Emotional Journey Mapping:
    - How emotions are triggered throughout the video
    - Emotional peaks and valleys
    - Resolution and satisfaction delivery
    - Trust-building techniques
    
    4. Attention Retention Strategies:
    - Pattern interrupts and surprise elements
    - Callback references to earlier points
    - Foreshadowing and preview techniques
    - Cliffhanger and open loop management
    
    5. Audience Connection Techniques:
    - Relatability factors and common experiences
    - Vulnerability and authenticity moments
    - Shared language and cultural references
    - Problem-solution alignment
    
    6. Call-to-Action Psychology:
    - Placement timing optimization
    - Motivation techniques used
    - Barrier reduction strategies
    - Multiple CTA layering
    
    7. Content Density Analysis:
    - Information-to-entertainment ratio
    - Learning curve management
    - Value delivery pacing
    - Cognitive rest periods
    
    Provide specific implementation guidelines for each psychological principle identified.
    Focus on techniques that would work best for {targetAudience} and {scriptStyle}.
  `,
  expectedOutput: `Advanced psychological script analysis with:
    1. Hook psychology breakdown with implementation examples
    2. Cognitive load optimization strategies
    3. Emotional journey template
    4. Attention retention toolkit
    5. Audience connection playbook
    6. CTA psychology guide
    7. Content density optimization framework
    8. Specific psychological triggers for the topic
    9. Implementation priority ranking
    10. A/B testing recommendations for script elements`,
  agent: captionAnalyserAgent
}); 