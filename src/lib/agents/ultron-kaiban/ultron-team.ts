/**
 * KaibanJS Ultron Team Configuration
 * Orchestrates the complete YouTube script generation workflow
 */

import { Team } from 'kaibanjs';
import { webSearchAgent, webSearchTask, advancedWebSearchTask } from './agents/web-search-agent';
import { captionAnalyserAgent, captionAnalysisTask, advancedCaptionAnalysisTask } from './agents/caption-analyser-agent';
import { writerAgent, scriptWritingTask, enhancedScriptWritingTask } from './agents/writer-agent';
import { supervisorAgent, supervisorCoordinationTask, qualityAssuranceTask } from './agents/supervisor-agent';
import type { UltronConfig } from './types';

/**
 * Standard Ultron Team for YouTube Script Generation
 * Workflow: Research → Analysis → Supervision → Writing → Quality Assurance
 */
export const ultronTeam = new Team({
  name: 'Ultron YouTube Script Generation Team',
  agents: [
    webSearchAgent,
    captionAnalyserAgent,
    supervisorAgent,
    writerAgent
  ],
  tasks: [
    webSearchTask,
    captionAnalysisTask,
    supervisorCoordinationTask,
    scriptWritingTask,
    qualityAssuranceTask
  ],
  inputs: {
    topic: '',
    targetAudience: 'General YouTube viewers',
    scriptStyle: 'educational',
    videoLength: 10,
    includeHooks: true,
    includeCallToAction: true
  },
  env: {
    GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
    TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''
  }
});

/**
 * Advanced Ultron Team with Enhanced Capabilities
 * Uses advanced tasks for more sophisticated analysis and writing
 */
export const advancedUltronTeam = new Team({
  name: 'Advanced Ultron YouTube Script Generation Team',
  agents: [
    webSearchAgent,
    captionAnalyserAgent,
    supervisorAgent,
    writerAgent
  ],
  tasks: [
    advancedWebSearchTask,
    advancedCaptionAnalysisTask,
    supervisorCoordinationTask,
    enhancedScriptWritingTask,
    qualityAssuranceTask
  ],
  inputs: {
    topic: '',
    targetAudience: 'General YouTube viewers',
    scriptStyle: 'educational',
    videoLength: 10,
    includeHooks: true,
    includeCallToAction: true
  },
  env: {
    GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
    TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''
  }
});

/**
 * Quick Script Team for Rapid Content Creation
 * Streamlined workflow for faster turnaround
 */
export const quickScriptTeam = new Team({
  name: 'Quick Script Generation Team',
  agents: [
    webSearchAgent,
    writerAgent
  ],
  tasks: [
    webSearchTask,
    scriptWritingTask
  ],
  inputs: {
    topic: '',
    targetAudience: 'General YouTube viewers',
    scriptStyle: 'educational',
    videoLength: 5,
    includeHooks: true,
    includeCallToAction: false
  },
  env: {
    GEMINI_API_KEY: process.env.GEMINI_API_KEY || '',
    TAVILY_API_KEY: process.env.TAVILY_API_KEY || '',
    OPENAI_API_KEY: process.env.OPENAI_API_KEY || ''
  }
});

/**
 * Utility functions for team management
 */

export interface UltronTeamConfig extends UltronConfig {
  teamType?: 'standard' | 'advanced' | 'quick';
}

/**
 * Simple function to start a team with given config
 */
export async function generateScript(config: UltronTeamConfig): Promise<{
  success: boolean;
  script?: string;
  error?: string;
  metadata?: any;
}> {
  try {
    let team: Team;
    
    // Select team based on type
    switch (config.teamType) {
      case 'advanced':
        team = advancedUltronTeam;
        break;
      case 'quick':
        team = quickScriptTeam;
        break;
      default:
        team = ultronTeam;
    }
    
    console.log(`🚀 Starting ${config.teamType || 'standard'} Ultron team for topic: ${config.topic}`);
    
    // Start the team workflow with inputs
    const result = await team.start({
      topic: config.topic,
      targetAudience: config.targetAudience || 'General YouTube viewers',
      scriptStyle: config.scriptStyle || 'educational',
      videoLength: config.videoLength || 10,
      includeHooks: config.includeHooks ?? true,
      includeCallToAction: config.includeCallToAction ?? true
    });
    
    if (result && result.result) {
      console.log('✅ Script generation completed successfully');
      return {
        success: true,
        script: result.result,
        metadata: {
          teamType: config.teamType || 'standard',
          topic: config.topic,
          targetAudience: config.targetAudience,
          scriptStyle: config.scriptStyle,
          videoLength: config.videoLength,
          timestamp: new Date().toISOString()
        }
      };
    } else {
      throw new Error('No result generated from team workflow');
    }
  } catch (error) {
    console.error('❌ Script generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Batch script generation for multiple topics
 */
export async function generateMultipleScripts(
  topics: string[], 
  baseConfig: Omit<UltronTeamConfig, 'topic'>
): Promise<Array<{
  topic: string;
  success: boolean;
  script?: string;
  error?: string;
  metadata?: any;
}>> {
  const results = [];
  
  for (const topic of topics) {
    console.log(`\n📝 Processing topic: ${topic}`);
    const result = await generateScript({ ...baseConfig, topic });
    results.push({ topic, ...result });
    
    // Add delay between requests to avoid rate limits
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  return results;
}

/**
 * Get team information
 */
export function getTeamInfo(teamType: 'standard' | 'advanced' | 'quick' = 'standard') {
  const teams = {
    standard: {
      name: 'Ultron YouTube Script Generation Team',
      agents: ['ResearcherAgent', 'CaptionAnalyserAgent', 'SupervisorAgent', 'ScriptWriterAgent'],
      tasks: ['Web Search', 'Caption Analysis', 'Supervision', 'Script Writing', 'Quality Assurance']
    },
    advanced: {
      name: 'Advanced Ultron YouTube Script Generation Team',
      agents: ['ResearcherAgent', 'CaptionAnalyserAgent', 'SupervisorAgent', 'ScriptWriterAgent'],
      tasks: ['Advanced Web Search', 'Advanced Caption Analysis', 'Supervision', 'Enhanced Script Writing', 'Quality Assurance']
    },
    quick: {
      name: 'Quick Script Generation Team',
      agents: ['ResearcherAgent', 'ScriptWriterAgent'],
      tasks: ['Web Search', 'Script Writing']
    }
  };
  
  return teams[teamType];
} 