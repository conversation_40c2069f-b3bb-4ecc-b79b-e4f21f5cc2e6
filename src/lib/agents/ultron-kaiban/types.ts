/**
 * Type definitions for KaibanJS Ultron Agent System
 */

export interface UltronConfig {
  topic: string;
  targetAudience?: string;
  videoLength?: number; // in minutes
  scriptStyle?: 'educational' | 'entertainment' | 'documentary' | 'tutorial' | 'review';
  includeHooks?: boolean;
  includeCallToAction?: boolean;
}

export interface WebSearchResult {
  url: string;
  title: string;
  content: string;
  relevanceScore: number;
  publishDate?: string;
  domain: string;
}

export interface YouTubeVideoData {
  videoId: string;
  title: string;
  description?: string;
  transcript?: string;
  viewCount?: number;
  duration?: string;
  publishDate?: string;
  channelName?: string;
}

export interface CaptionAnalysis {
  writingStyle: {
    tone: string;
    pacing: string;
    vocabulary: string;
    sentenceStructure: string;
  };
  hooks: {
    openingHook: string;
    midPointHooks: string[];
    endingHook: string;
  };
  engagementTactics: {
    questions: string[];
    callouts: string[];
    transitions: string[];
  };
  contentStructure: {
    introduction: string;
    mainPoints: string[];
    conclusion: string;
  };
  narrationStyle: {
    perspective: string; // first person, second person, third person
    energy: string; // high, medium, low
    expertise: string; // beginner-friendly, intermediate, advanced
  };
}

export interface ScriptSection {
  type: 'hook' | 'introduction' | 'main_point' | 'transition' | 'conclusion' | 'call_to_action';
  content: string;
  duration?: number; // estimated seconds
  notes?: string;
}

export interface GeneratedScript {
  title: string;
  description: string;
  sections: ScriptSection[];
  totalDuration: number;
  metadata: {
    topic: string;
    targetAudience: string;
    style: string;
    keyPoints: string[];
    hooks: string[];
  };
}

export interface SupervisorInstructions {
  researchFindings: WebSearchResult[];
  analysisResults: CaptionAnalysis[];
  writerGuidance: {
    tone: string;
    structure: string[];
    keyPoints: string[];
    hooks: string[];
    callToAction?: string;
  };
  qualityChecks: string[];
} 