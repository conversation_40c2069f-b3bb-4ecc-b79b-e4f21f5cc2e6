/**
 * Smart URL Analysis System for All Content Types
 * 
 * Implements intelligent URL analysis that adapts to any content type:
 * - Alternatives/Comparisons: Analyzes comparison criteria and missing options
 * - Guides/Tutorials: Extracts step categories and tools needed
 * - Informational: Identifies key topics and knowledge gaps
 * - Reviews: Analyzes evaluation criteria and user sentiment
 * - Listicles: Extracts list items and categorization patterns
 * - News: Identifies trends and factual information
 */

import { OpenRouterService } from '../../openrouter';
import { ScrapedPageData } from './enhanced-research-state';

// ============================================================================
// SMART URL ANALYSIS INTERFACES
// ============================================================================

export interface ContentTypeAnalysis {
  // Universal content analysis
  contentType: 'alternatives' | 'guide' | 'tutorial' | 'review' | 'comparison' | 'listicle' | 'informational' | 'news' | 'opinion';
  confidence: number;
  primaryFocus: string;
  targetAudience: string;
  
  // Content type specific insights
  typeSpecificInsights: {
    // For alternatives/comparisons
    alternatives?: {
      mentionedOptions: Array<{
        name: string;
        category: string;
        keyFeatures: string[];
        pros: string[];
        cons: string[];
        pricing?: string;
        targetUser?: string;
      }>;
      comparisonCriteria: string[];
      evaluationFactors: string[];
      missingAlternatives: string[];
      marketGaps: string[];
    };
    
    // For guides/tutorials
    guidance?: {
      stepCategories: Array<{
        category: string;
        steps: string[];
        difficulty: 'beginner' | 'intermediate' | 'advanced';
        timeRequired?: string;
      }>;
      prerequisites: string[];
      toolsAndResources: Array<{
        tool: string;
        purpose: string;
        alternatives: string[];
        cost?: string;
      }>;
      commonChallenges: string[];
      successMetrics: string[];
    };
    
    // For informational content
    information?: {
      keyTopics: Array<{
        topic: string;
        depth: 'surface' | 'detailed' | 'comprehensive';
        subtopics: string[];
        dataPoints: string[];
      }>;
      knowledgeAreas: string[];
      expertiseLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
      factualClaims: string[];
      sourcedInformation: string[];
    };
    
    // For reviews
    reviews?: {
      reviewedItems: Array<{
        item: string;
        category: string;
        rating?: number;
        criteria: string[];
        highlights: string[];
        drawbacks: string[];
      }>;
      evaluationCriteria: string[];
      ratingSystem: string;
      userExperience: string[];
      recommendations: string[];
    };
    
    // For listicles
    listicles?: {
      listItems: Array<{
        item: string;
        description: string;
        ranking?: number;
        category?: string;
        keyPoints: string[];
      }>;
      listingCriteria: string;
      organizationPattern: string;
      completeness: number;
      missingItems: string[];
    };
    
    // For news/trends
    news?: {
      newsTopics: string[];
      timeframe: string;
      factualEvents: string[];
      trends: Array<{
        trend: string;
        direction: 'rising' | 'declining' | 'stable';
        impact: string;
        timeframe: string;
      }>;
      sources: string[];
      recency: number;
    };
  };
}

export interface UniversalInsights {
  // Common patterns across all content types
  commonQuestions: string[];
  userNeeds: string[];
  informationGaps: string[];
  searchIntents: string[];
  
  // Quality and depth analysis
  contentQuality: {
    depth: 'shallow' | 'moderate' | 'deep' | 'comprehensive';
    accuracy: number;
    freshness: number;
    comprehensiveness: number;
    uniqueness: number;
  };
  
  // Competitive insights
  competitiveAnalysis: {
    strengths: string[];
    weaknesses: string[];
    opportunities: string[];
    differentiationPotential: string[];
  };
  
  // SEO and user experience
  contentOptimization: {
    seoOpportunities: string[];
    userExperienceGaps: string[];
    structuralImprovements: string[];
    contentGaps: string[];
  };
}

export interface SmartAnalysisResult {
  // URL-specific analysis
  url: string;
  title: string;
  contentTypeAnalysis: ContentTypeAnalysis;
  
  // Data extraction
  extractedData: {
    keyStatistics: Array<{
      statistic: string;
      context: string;
      source: string;
      reliability: number;
    }>;
    importantFacts: Array<{
      fact: string;
      category: string;
      confidence: number;
      verification: string;
    }>;
    actionableInsights: string[];
    quotableContent: string[];
  };
  
  // Universal insights
  universalInsights: UniversalInsights;
  
  // Query generation recommendations
  queryRecommendations: Array<{
    query: string;
    purpose: string;
    priority: 'high' | 'medium' | 'low';
    category: string;
    expectedOutcome: string;
  }>;
  
  // Quality and relevance scores
  relevanceScore: number;
  qualityScore: number;
  uniquenessScore: number;
  
  // Analysis metadata
  analysisMetadata: {
    analysisTime: number;
    confidenceLevel: number;
    recommendedUse: string[];
    limitations: string[];
  };
}

// ============================================================================
// SMART URL ANALYZER CLASS
// ============================================================================

export class SmartUrlAnalyzer {
  private openRouterService: OpenRouterService;
  
  constructor() {
    this.openRouterService = new OpenRouterService({
      model: 'moonshotai/kimi-k2',
      temperature: 0.7,
      maxTokens: 8000
    });
  }

  /**
   * Analyze multiple URLs with intelligent categorization
   */
  async analyzeUrls(
    scrapedPages: ScrapedPageData[],
    userGoal: string,
    expectedContentType?: string
  ): Promise<SmartAnalysisResult[]> {
    console.log(`🧠 Smart URL Analyzer: Analyzing ${scrapedPages.length} URLs for goal: "${userGoal}"`);
    
    const analysisPromises = scrapedPages.map(async (page, index) => {
      try {
        console.log(`📊 Analyzing URL ${index + 1}/${scrapedPages.length}: ${new URL(page.url).hostname}`);
        return await this.analyzeUrl(page, userGoal, expectedContentType);
      } catch (error) {
        console.error(`❌ Failed to analyze URL ${page.url}:`, error);
        return this.createFallbackAnalysis(page, userGoal);
      }
    });

    const results = await Promise.all(analysisPromises);
    
    console.log(`✅ Smart URL Analysis complete: ${results.length} URLs analyzed`);
    return results;
  }

  /**
   * Analyze a single URL with comprehensive intelligence
   */
  async analyzeUrl(
    page: ScrapedPageData,
    userGoal: string,
    expectedContentType?: string
  ): Promise<SmartAnalysisResult> {
    
    // Phase 1: Content Type Detection
    const contentTypeAnalysis = await this.detectContentType(page, userGoal, expectedContentType);
    
    // Phase 2: Type-Specific Data Extraction
    const extractedData = await this.extractTypeSpecificData(page, contentTypeAnalysis);
    
    // Phase 3: Universal Insights Generation
    const universalInsights = await this.generateUniversalInsights(page, contentTypeAnalysis);
    
    // Phase 4: Query Recommendations
    const queryRecommendations = await this.generateQueryRecommendations(
      page, 
      contentTypeAnalysis, 
      universalInsights, 
      userGoal
    );
    
    // Calculate scores
    const relevanceScore = this.calculateRelevanceScore(page, userGoal, contentTypeAnalysis);
    const qualityScore = this.calculateQualityScore(page, extractedData);
    const uniquenessScore = this.calculateUniquenessScore(page, extractedData);
    
    return {
      url: page.url,
      title: page.title,
      contentTypeAnalysis,
      extractedData,
      universalInsights,
      queryRecommendations,
      relevanceScore,
      qualityScore,
      uniquenessScore,
      analysisMetadata: {
        analysisTime: Date.now(),
        confidenceLevel: contentTypeAnalysis.confidence,
        recommendedUse: this.getRecommendedUse(contentTypeAnalysis),
        limitations: this.getAnalysisLimitations(page, contentTypeAnalysis),
      }
    };
  }

  /**
   * Phase 1: Intelligent Content Type Detection
   */
  private async detectContentType(
    page: ScrapedPageData,
    userGoal: string,
    expectedContentType?: string
  ): Promise<ContentTypeAnalysis> {
    
    const detectionPrompt = `SMART CONTENT TYPE DETECTION & ANALYSIS

Analyze this content to understand its type and extract type-specific insights for goal: "${userGoal}"

CONTENT TO ANALYZE:
Title: ${page.title}
URL: ${page.url}
Content: ${page.content.substring(0, 2000)}...

ANALYSIS TASKS:
1. **Content Type Detection**: Determine the primary content type and confidence level
2. **Type-Specific Analysis**: Extract insights specific to the detected content type
3. **Target Audience**: Identify who this content is written for
4. **Primary Focus**: What is the main focus/purpose of this content

CONTENT TYPES TO CONSIDER:
- alternatives: Comparing multiple options/tools/services
- guide: Step-by-step instructions for achieving something
- tutorial: Educational content teaching specific skills
- review: Evaluation/assessment of products/services
- comparison: Direct comparison between items
- listicle: List-based content (top 10, best X, etc.)
- informational: Educational/explanatory content
- news: Current events, announcements, updates
- opinion: Subjective views and commentary

Return ONLY valid JSON with type-specific insights:
{
  "contentType": "detected_type",
  "confidence": 95,
  "primaryFocus": "main focus of content",
  "targetAudience": "audience description",
  "typeSpecificInsights": {
    "alternatives": {
      "mentionedOptions": [
        {
          "name": "option name",
          "category": "category",
          "keyFeatures": ["feature1", "feature2"],
          "pros": ["pro1", "pro2"],
          "cons": ["con1", "con2"],
          "pricing": "price info",
          "targetUser": "user type"
        }
      ],
      "comparisonCriteria": ["criteria1", "criteria2"],
      "evaluationFactors": ["factor1", "factor2"],
      "missingAlternatives": ["missing1", "missing2"],
      "marketGaps": ["gap1", "gap2"]
    }
  }
}`;

    try {
      const response = await this.openRouterService.generateContent(detectionPrompt);
      const analysis = this.parseJsonResponse(response.response, 'Content Type Detection');
      
      // Validate and ensure required fields
      return {
        contentType: analysis.contentType || 'informational',
        confidence: Math.min(Math.max(analysis.confidence || 70, 0), 100),
        primaryFocus: analysis.primaryFocus || 'General information',
        targetAudience: analysis.targetAudience || 'General audience',
        typeSpecificInsights: analysis.typeSpecificInsights || {}
      };
      
    } catch (error) {
      console.warn('⚠️ Content type detection failed, using fallback');
      return this.createFallbackContentTypeAnalysis(page, expectedContentType);
    }
  }

  /**
   * Phase 2: Type-Specific Data Extraction
   */
  private async extractTypeSpecificData(
    page: ScrapedPageData,
    contentTypeAnalysis: ContentTypeAnalysis
  ): Promise<SmartAnalysisResult['extractedData']> {
    
    const extractionPrompt = `TYPE-SPECIFIC DATA EXTRACTION

Extract specific data points from this ${contentTypeAnalysis.contentType} content:

CONTENT:
${page.content.substring(0, 1500)}...

EXTRACTION FOCUS FOR ${contentTypeAnalysis.contentType.toUpperCase()}:
1. **Key Statistics**: Numbers, percentages, measurements, data points
2. **Important Facts**: Verifiable information and claims
3. **Actionable Insights**: Practical takeaways and recommendations
4. **Quotable Content**: Notable quotes, expert opinions, key statements

Return ONLY valid JSON:
{
  "keyStatistics": [
    {
      "statistic": "specific number or percentage",
      "context": "what this statistic means",
      "source": "where it came from",
      "reliability": 8
    }
  ],
  "importantFacts": [
    {
      "fact": "factual statement",
      "category": "category of fact",
      "confidence": 9,
      "verification": "how this can be verified"
    }
  ],
  "actionableInsights": ["insight1", "insight2"],
  "quotableContent": ["quote1", "quote2"]
}`;

    try {
      const response = await this.openRouterService.generateContent(extractionPrompt);
      const data = this.parseJsonResponse(response.response, 'Data Extraction');
      
      return {
        keyStatistics: data.keyStatistics || [],
        importantFacts: data.importantFacts || [],
        actionableInsights: data.actionableInsights || [],
        quotableContent: data.quotableContent || []
      };
      
    } catch (error) {
      console.warn('⚠️ Data extraction failed, using fallback');
      return {
        keyStatistics: [],
        importantFacts: [],
        actionableInsights: [`General insights from ${contentTypeAnalysis.contentType} content`],
        quotableContent: []
      };
    }
  }

  /**
   * Phase 3: Universal Insights Generation
   */
  private async generateUniversalInsights(
    page: ScrapedPageData,
    contentTypeAnalysis: ContentTypeAnalysis
  ): Promise<UniversalInsights> {
    
    const insightsPrompt = `UNIVERSAL INSIGHTS GENERATION

Analyze this content to generate universal insights that apply regardless of content type:

CONTENT TYPE: ${contentTypeAnalysis.contentType}
CONTENT: ${page.content.substring(0, 1200)}...

GENERATE INSIGHTS FOR:
1. **User Needs & Questions**: What do users want to know?
2. **Information Gaps**: What's missing or could be improved?
3. **Quality Assessment**: Depth, accuracy, freshness, comprehensiveness
4. **Competitive Analysis**: Strengths, weaknesses, opportunities
5. **Content Optimization**: SEO, UX, structural improvements

Return ONLY valid JSON:
{
  "commonQuestions": ["question1", "question2"],
  "userNeeds": ["need1", "need2"],
  "informationGaps": ["gap1", "gap2"],
  "searchIntents": ["intent1", "intent2"],
  "contentQuality": {
    "depth": "moderate|deep|comprehensive",
    "accuracy": 85,
    "freshness": 90,
    "comprehensiveness": 80,
    "uniqueness": 75
  },
  "competitiveAnalysis": {
    "strengths": ["strength1", "strength2"],
    "weaknesses": ["weakness1", "weakness2"],
    "opportunities": ["opportunity1", "opportunity2"],
    "differentiationPotential": ["diff1", "diff2"]
  },
  "contentOptimization": {
    "seoOpportunities": ["seo1", "seo2"],
    "userExperienceGaps": ["ux1", "ux2"],
    "structuralImprovements": ["struct1", "struct2"],
    "contentGaps": ["gap1", "gap2"]
  }
}`;

    try {
      const response = await this.openRouterService.generateContent(insightsPrompt);
      const insights = this.parseJsonResponse(response.response, 'Universal Insights');
      
      return {
        commonQuestions: insights.commonQuestions || [],
        userNeeds: insights.userNeeds || [],
        informationGaps: insights.informationGaps || [],
        searchIntents: insights.searchIntents || [],
        contentQuality: {
          depth: insights.contentQuality?.depth || 'moderate',
          accuracy: insights.contentQuality?.accuracy || 75,
          freshness: insights.contentQuality?.freshness || 70,
          comprehensiveness: insights.contentQuality?.comprehensiveness || 70,
          uniqueness: insights.contentQuality?.uniqueness || 70
        },
        competitiveAnalysis: {
          strengths: insights.competitiveAnalysis?.strengths || [],
          weaknesses: insights.competitiveAnalysis?.weaknesses || [],
          opportunities: insights.competitiveAnalysis?.opportunities || [],
          differentiationPotential: insights.competitiveAnalysis?.differentiationPotential || []
        },
        contentOptimization: {
          seoOpportunities: insights.contentOptimization?.seoOpportunities || [],
          userExperienceGaps: insights.contentOptimization?.userExperienceGaps || [],
          structuralImprovements: insights.contentOptimization?.structuralImprovements || [],
          contentGaps: insights.contentOptimization?.contentGaps || []
        }
      };
      
    } catch (error) {
      console.warn('⚠️ Universal insights generation failed, using fallback');
      return this.createFallbackUniversalInsights(contentTypeAnalysis);
    }
  }

  /**
   * Phase 4: Query Recommendations Generation
   */
  private async generateQueryRecommendations(
    page: ScrapedPageData,
    contentTypeAnalysis: ContentTypeAnalysis,
    universalInsights: UniversalInsights,
    userGoal: string
  ): Promise<SmartAnalysisResult['queryRecommendations']> {
    
    const queryPrompt = `INTELLIGENT QUERY RECOMMENDATIONS

Based on the analysis, generate smart research queries to improve content for: "${userGoal}"

CONTENT TYPE: ${contentTypeAnalysis.contentType}
IDENTIFIED GAPS: ${universalInsights.informationGaps.join(', ')}
USER NEEDS: ${universalInsights.userNeeds.join(', ')}
OPPORTUNITIES: ${universalInsights.competitiveAnalysis.opportunities.join(', ')}

GENERATE RESEARCH QUERIES THAT:
1. Fill identified information gaps
2. Address user needs and questions
3. Leverage competitive opportunities
4. Enhance content depth and quality
5. Provide unique value

Return ONLY valid JSON:
{
  "queryRecommendations": [
    {
      "query": "specific search query",
      "purpose": "why this query is needed",
      "priority": "high|medium|low",
      "category": "data|trends|alternatives|examples|tools",
      "expectedOutcome": "what we expect to find"
    }
  ]
}`;

    try {
      const response = await this.openRouterService.generateContent(queryPrompt);
      const recommendations = this.parseJsonResponse(response.response, 'Query Recommendations');
      
      return recommendations.queryRecommendations || [];
      
    } catch (error) {
      console.warn('⚠️ Query recommendations generation failed, using fallback');
      return this.createFallbackQueryRecommendations(contentTypeAnalysis, userGoal);
    }
  }

  // ============================================================================
  // SCORING & UTILITY METHODS
  // ============================================================================

  private calculateRelevanceScore(
    page: ScrapedPageData,
    userGoal: string,
    contentTypeAnalysis: ContentTypeAnalysis
  ): number {
    const goalWords = userGoal.toLowerCase().split(' ');
    const contentWords = (page.title + ' ' + page.content.substring(0, 500)).toLowerCase();
    
    const matches = goalWords.filter(word => 
      word.length > 3 && contentWords.includes(word)
    ).length;
    
    const relevanceBase = Math.min((matches / goalWords.length) * 100, 90);
    const confidenceBonus = contentTypeAnalysis.confidence * 0.1;
    
    return Math.round(relevanceBase + confidenceBonus);
  }

  private calculateQualityScore(
    page: ScrapedPageData,
    extractedData: SmartAnalysisResult['extractedData']
  ): number {
    const dataRichness = (extractedData.keyStatistics.length * 10) + 
                        (extractedData.importantFacts.length * 5) + 
                        (extractedData.actionableInsights.length * 3);
    
    const contentLength = page.wordCount;
    const lengthScore = Math.min(contentLength / 20, 50); // Max 50 points for length
    const dataScore = Math.min(dataRichness, 40); // Max 40 points for data richness
    
    return Math.round(lengthScore + dataScore + 10); // Base 10 points
  }

  private calculateUniquenessScore(
    page: ScrapedPageData,
    extractedData: SmartAnalysisResult['extractedData']
  ): number {
    const uniqueElements = extractedData.quotableContent.length + 
                          extractedData.keyStatistics.length + 
                          extractedData.actionableInsights.length;
    
    return Math.min(uniqueElements * 8 + 20, 100);
  }

  private getRecommendedUse(contentTypeAnalysis: ContentTypeAnalysis): string[] {
    const uses = ['competitive-research', 'content-inspiration'];
    
    if (contentTypeAnalysis.confidence > 80) {
      uses.push('reliable-reference');
    }
    
    if (contentTypeAnalysis.contentType === 'alternatives') {
      uses.push('comparison-baseline', 'market-analysis');
    } else if (contentTypeAnalysis.contentType === 'guide') {
      uses.push('structural-reference', 'process-inspiration');
    }
    
    return uses;
  }

  private getAnalysisLimitations(
    page: ScrapedPageData,
    contentTypeAnalysis: ContentTypeAnalysis
  ): string[] {
    const limitations = [];
    
    if (page.wordCount < 500) {
      limitations.push('limited-content-length');
    }
    
    if (contentTypeAnalysis.confidence < 70) {
      limitations.push('uncertain-content-type');
    }
    
    if (!page.content.includes('2024') && !page.content.includes('2025')) {
      limitations.push('potentially-outdated');
    }
    
    return limitations;
  }

  // ============================================================================
  // FALLBACK METHODS
  // ============================================================================

  private createFallbackAnalysis(page: ScrapedPageData, userGoal: string): SmartAnalysisResult {
    return {
      url: page.url,
      title: page.title,
      contentTypeAnalysis: this.createFallbackContentTypeAnalysis(page),
      extractedData: {
        keyStatistics: [],
        importantFacts: [],
        actionableInsights: ['Content available for reference'],
        quotableContent: []
      },
      universalInsights: this.createFallbackUniversalInsights(),
      queryRecommendations: this.createFallbackQueryRecommendations(undefined, userGoal),
      relevanceScore: 50,
      qualityScore: 40,
      uniquenessScore: 30,
      analysisMetadata: {
        analysisTime: Date.now(),
        confidenceLevel: 30,
        recommendedUse: ['fallback-reference'],
        limitations: ['analysis-failed', 'limited-insights']
      }
    };
  }

  private createFallbackContentTypeAnalysis(
    page: ScrapedPageData,
    expectedContentType?: string
  ): ContentTypeAnalysis {
    return {
      contentType: (expectedContentType as any) || 'informational',
      confidence: 50,
      primaryFocus: 'General information',
      targetAudience: 'General audience',
      typeSpecificInsights: {}
    };
  }

  private createFallbackUniversalInsights(
    contentTypeAnalysis?: ContentTypeAnalysis
  ): UniversalInsights {
    return {
      commonQuestions: ['What are the key points?', 'How does this work?'],
      userNeeds: ['Clear information', 'Practical guidance'],
      informationGaps: ['More detailed explanations needed'],
      searchIntents: ['Learn more about the topic'],
      contentQuality: {
        depth: 'moderate',
        accuracy: 70,
        freshness: 60,
        comprehensiveness: 60,
        uniqueness: 50
      },
      competitiveAnalysis: {
        strengths: ['Basic information provided'],
        weaknesses: ['Limited depth'],
        opportunities: ['Expand on key points'],
        differentiationPotential: ['Add unique insights']
      },
      contentOptimization: {
        seoOpportunities: ['Better keyword targeting'],
        userExperienceGaps: ['Improve readability'],
        structuralImprovements: ['Better organization'],
        contentGaps: ['More comprehensive coverage']
      }
    };
  }

  private createFallbackQueryRecommendations(
    contentTypeAnalysis?: ContentTypeAnalysis,
    userGoal?: string
  ): SmartAnalysisResult['queryRecommendations'] {
    return [
      {
        query: `${userGoal} best practices`,
        purpose: 'Find industry best practices',
        priority: 'medium',
        category: 'general',
        expectedOutcome: 'Comprehensive guidance'
      },
      {
        query: `${userGoal} examples`,
        purpose: 'Find practical examples',
        priority: 'medium',
        category: 'examples',
        expectedOutcome: 'Real-world examples'
      }
    ];
  }

  private parseJsonResponse(response: string, context: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      throw new Error('No JSON found in response');
    } catch (error) {
      console.warn(`⚠️ Failed to parse JSON for ${context}:`, error);
      return {};
    }
  }

  /**
   * Get aggregated insights from multiple URL analyses
   */
  aggregateInsights(analyses: SmartAnalysisResult[]): {
    overallContentTypes: { [key: string]: number };
    commonPatterns: string[];
    aggregatedGaps: string[];
    priorityQueries: SmartAnalysisResult['queryRecommendations'];
    competitiveOpportunities: string[];
  } {
    const contentTypes: { [key: string]: number } = {};
    const allGaps: string[] = [];
    const allQueries: SmartAnalysisResult['queryRecommendations'] = [];
    const allOpportunities: string[] = [];
    
    analyses.forEach(analysis => {
      // Count content types
      contentTypes[analysis.contentTypeAnalysis.contentType] = 
        (contentTypes[analysis.contentTypeAnalysis.contentType] || 0) + 1;
      
      // Collect gaps
      allGaps.push(...analysis.universalInsights.informationGaps);
      
      // Collect queries
      allQueries.push(...analysis.queryRecommendations);
      
      // Collect opportunities
      allOpportunities.push(...analysis.universalInsights.competitiveAnalysis.opportunities);
    });
    
    // Get top priority queries
    const priorityQueries = allQueries
      .filter(q => q.priority === 'high')
      .slice(0, 10);
    
    return {
      overallContentTypes: contentTypes,
      commonPatterns: this.findCommonPatterns(analyses),
      aggregatedGaps: [...new Set(allGaps)],
      priorityQueries,
      competitiveOpportunities: [...new Set(allOpportunities)]
    };
  }

  private findCommonPatterns(analyses: SmartAnalysisResult[]): string[] {
    const patterns: { [key: string]: number } = {};
    
    analyses.forEach(analysis => {
      analysis.universalInsights.userNeeds.forEach(need => {
        patterns[need] = (patterns[need] || 0) + 1;
      });
    });
    
    return Object.entries(patterns)
      .filter(([_, count]) => count >= Math.ceil(analyses.length * 0.3))
      .map(([pattern, _]) => pattern);
  }
}