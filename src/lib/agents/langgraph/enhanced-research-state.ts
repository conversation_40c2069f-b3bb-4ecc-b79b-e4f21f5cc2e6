/**
 * Enhanced Research State for LangGraph-based Multi-Agent Content System
 * 
 * Implements the user's vision:
 * 1. Quick Tavily search for exact topic
 * 2. Parallel web scraping of 10 URLs with data extraction
 * 3. Three-phase analysis: context understanding, data extraction, query generation
 * 4. Intelligent planning and content generation with supervisor coordination
 */

import { Annotation } from "@langchain/langgraph";

// ============================================================================
// ENHANCED RESEARCH INTERFACES
// ============================================================================

export interface ScrapedPageData {
  url: string;
  title: string;
  content: string;
  wordCount: number;
  scrapedAt: number;
  
  // Enhanced extraction data
  extractedData: {
    // Stage 1: Topic Understanding
    mainTopic: string;
    articleType: string;
    targetAudience: string;
    contentComplexity: 'beginner' | 'intermediate' | 'advanced';
    
    // Stage 2: Data Points
    keyStatistics: Array<{
      statistic: string;
      source: string;
      importance: number;
    }>;
    importantFacts: Array<{
      fact: string;
      category: string;
      verified: boolean;
    }>;
    trends: Array<{
      trend: string;
      timeframe: string;
      significance: string;
    }>;
    
    // Stage 3: Query Generation Insights
    topicCoverage: string[];
    missingElements: string[];
    competitorFocus: string[];
    expertiseAreas: string[];
  };
}

export interface IntelligenceAnalysis {
  // Stage 1: Topic Context Understanding
  topicAnalysis: {
    primaryTopic: string;
    topicVariations: string[];
    articleTypeDetected: string;
    targetAudienceProfile: string;
    contentComplexity: 'beginner' | 'intermediate' | 'advanced';
    purposeIdentified: string;
    competitorsIdentified: string[];
  };
  
  // Stage 2: Data & Statistics Extraction  
  dataAnalysis: {
    keyStatistics: Array<{
      statistic: string;
      source: string;
      importance: number;
      category: string;
    }>;
    importantFacts: Array<{
      fact: string;
      category: string;
      sources: string[];
      confidence: number;
    }>;
    trends: Array<{
      trend: string;
      timeframe: string;
      significance: string;
      dataPoints: string[];
    }>;
    commonDataPoints: string[];
    dataGaps: string[];
  };
  
  // Stage 3: Query Generation Intelligence
  queryIntelligence: {
    // Content type specific analysis
    contentTypeInsights: {
      // For alternatives/comparisons
      alternatives?: {
        mentionedAlternatives: string[];
        keyComparisonPoints: string[];
        evaluationCriteria: string[];
        missingAlternatives: string[];
        competitiveAdvantages: string[];
      };
      
      // For guides/tutorials  
      guidance?: {
        stepCategories: string[];
        commonChallenges: string[];
        toolsNeeded: string[];
        skillLevels: string[];
        prerequisites: string[];
      };
      
      // For informational content
      information?: {
        keyTopics: string[];
        subtopicAreas: string[];
        expertiseAreas: string[];
        knowledgeGaps: string[];
        relatedConcepts: string[];
      };
    };
    
    // Universal insights for query generation
    universalInsights: {
      commonQuestions: string[];
      informationGaps: string[];
      deeperTopics: string[];
      relatedConcepts: string[];
      searchPatterns: string[];
      userIntents: string[];
    };
    
    // Generated research queries based on analysis
    intelligentQueries: Array<{
      query: string;
      purpose: string;
      priority: 'high' | 'medium' | 'low';
      category: string;
      expectedFindings: string[];
      searchStrategy: string;
    }>;
  };
}

export interface ContentStrategy {
  // Content planning based on analysis
  approach: string;
  uniqueAngle: string;
  targetWordCount: number;
  estimatedComplexity: number;
  
  // Content structure
  requiredSections: Array<{
    title: string;
    purpose: string;
    priority: number;
    dataRequired: string[];
  }>;
  
  // Quality targets
  qualityTargets: {
    researchDepth: number;
    dataRichness: number;
    uniqueness: number;
    comprehensiveness: number;
  };
  
  // Competitive positioning
  competitiveStrategy: {
    differentiators: string[];
    improvementAreas: string[];
    valueProposition: string;
  };
}

export interface SupervisorDecision {
  phase: string;
  decision: 'proceed' | 'retry' | 'escalate' | 'pivot' | 'abort';
  reasoning: string;
  recommendations: string[];
  qualityScore: number;
  nextAction: string;
  timestamp: number;
}

// ============================================================================
// ENHANCED LANGGRAPH STATE SCHEMA
// ============================================================================

export const EnhancedResearchState = Annotation.Root({
  // ===== INPUT CONFIGURATION =====
  goal: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  contentType: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  targetAudience: Annotation<string>({
    reducer: (x, y) => y ?? x ?? 'general audience',
  }),
  
  tone: Annotation<string>({
    reducer: (x, y) => y ?? x ?? 'professional',
  }),
  
  targetWordCount: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 1500,
  }),
  
  // ===== RESEARCH PIPELINE DATA =====
  
  // Phase 1: Initial Tavily Search
  initialSearchResults: Annotation<Array<{
    url: string;
    title: string;
    snippet: string;
    published_date?: string;
    score: number;
  }>>({
    reducer: (x, y) => y ?? x ?? [],
  }),
  
  // Phase 2: Parallel Web Scraping Results
  scrapedPages: Annotation<ScrapedPageData[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  scrapingProgress: Annotation<{
    total: number;
    completed: number;
    failed: number;
    inProgress: string[];
  }>({
    reducer: (x, y) => y ?? x ?? { total: 0, completed: 0, failed: 0, inProgress: [] },
  }),
  
  // Phase 3: Three-Stage Analysis Results
  intelligenceAnalysis: Annotation<IntelligenceAnalysis | null>({
    reducer: (x, y) => y ?? x,
  }),
  
  analysisProgress: Annotation<{
    stage1Complete: boolean;
    stage2Complete: boolean;
    stage3Complete: boolean;
    currentStage: number;
  }>({
    reducer: (x, y) => y ?? x ?? { 
      stage1Complete: false, 
      stage2Complete: false, 
      stage3Complete: false, 
      currentStage: 1 
    },
  }),
  
  // ===== INTELLIGENT PLANNING =====
  contentStrategy: Annotation<ContentStrategy | null>({
    reducer: (x, y) => y ?? x,
  }),
  
  researchQueries: Annotation<Array<{
    query: string;
    purpose: string;
    priority: 'high' | 'medium' | 'low';
    status: 'pending' | 'completed' | 'failed';
    results?: any[];
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // ===== CONTENT GENERATION =====
  researchData: Annotation<Array<{
    query: string;
    results: any[];
    relevanceScore: number;
    extractedInsights: string[];
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  competitorAnalysis: Annotation<Array<{
    url: string;
    title: string;
    wordCount: number;
    structure: string[];
    strengths: string[];
    gaps: string[];
    seoScore: number;
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  contentDraft: Annotation<{
    title: string;
    content: string;
    wordCount: number;
    sections: string[];
    qualityMetrics: {
      research: number;
      writing: number;
      seo: number;
      readability: number;
      uniqueness: number;
    };
  } | null>({
    reducer: (x, y) => y ?? x,
  }),
  
  // ===== SUPERVISOR COORDINATION =====
  currentPhase: Annotation<'initialization' | 'tavily_search' | 'web_scraping' | 'analysis_stage1' | 'analysis_stage2' | 'analysis_stage3' | 'planning' | 'research' | 'competition' | 'writing' | 'quality' | 'completion' | 'failed'>({
    reducer: (x, y) => y ?? x ?? 'initialization',
  }),
  
  supervisorDecisions: Annotation<SupervisorDecision[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  qualityGates: Annotation<Array<{
    phase: string;
    passed: boolean;
    score: number;
    issues: string[];
    recommendations: string[];
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // ===== EXECUTION CONTROL =====
  retryCount: Annotation<number>({
    reducer: (x, y) => (y ?? 0) + (x ?? 0),
  }),
  
  maxRetries: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 3,
  }),
  
  qualityThreshold: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 85,
  }),
  
  timeoutMinutes: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 15,
  }),
  
  // ===== ERROR HANDLING & LOGGING =====
  errors: Annotation<Array<{
    phase: string;
    error: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: number;
    recovery?: string;
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  logs: Annotation<Array<{
    agent: string;
    phase: string;
    message: string;
    timestamp: number;
    level: 'debug' | 'info' | 'warn' | 'error';
  }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // ===== PERFORMANCE METRICS =====
  performanceMetrics: Annotation<{
    startTime: number;
    endTime?: number;
    totalDuration?: number;
    phaseMetrics: Record<string, {
      startTime: number;
      endTime?: number;
      duration?: number;
      retries: number;
    }>;
    apiCalls: {
      tavily: number;
      openrouter: number;
      scraping: number;
    };
    dataMetrics: {
      urlsScraped: number;
      totalContent: number;
      analysisDepth: number;
      queriesGenerated: number;
    };
  }>({
    reducer: (x, y) => y ?? x ?? {
      startTime: Date.now(),
      phaseMetrics: {},
      apiCalls: { tavily: 0, openrouter: 0, scraping: 0 },
      dataMetrics: { urlsScraped: 0, totalContent: 0, analysisDepth: 0, queriesGenerated: 0 }
    },
  }),
  
  // ===== FINAL OUTPUT =====
  finalResult: Annotation<{
    success: boolean;
    content: {
      title: string;
      content: string;
      wordCount: number;
      qualityScore: number;
    };
    metadata: {
      researchDepth: number;
      dataPoints: number;
      competitorAnalysis: number;
      uniquenessScore: number;
    };
    executionSummary: {
      totalTime: number;
      phasesCompleted: string[];
      qualityGates: number;
      decisionsCount: number;
    };
  } | null>({
    reducer: (x, y) => y ?? x,
  }),
  
  success: Annotation<boolean>({
    reducer: (x, y) => y ?? x ?? false,
  }),
});

export type EnhancedResearchStateType = typeof EnhancedResearchState.State;

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

export function createInitialEnhancedState(
  goal: string,
  options: {
    contentType?: string;
    targetAudience?: string;
    tone?: string;
    targetWordCount?: number;
    qualityThreshold?: number;
    maxRetries?: number;
    timeoutMinutes?: number;
  } = {}
): Partial<EnhancedResearchStateType> {
  return {
    goal,
    contentType: options.contentType || 'article',
    targetAudience: options.targetAudience || 'general audience',
    tone: options.tone || 'professional',
    targetWordCount: options.targetWordCount || 1500,
    qualityThreshold: options.qualityThreshold || 85,
    maxRetries: options.maxRetries || 3,
    timeoutMinutes: options.timeoutMinutes || 15,
    currentPhase: 'initialization',
    retryCount: 0,
    initialSearchResults: [],
    scrapedPages: [],
    researchQueries: [],
    researchData: [],
    competitorAnalysis: [],
    supervisorDecisions: [],
    qualityGates: [],
    errors: [],
    logs: [],
    success: false,
    performanceMetrics: {
      startTime: Date.now(),
      phaseMetrics: {},
      apiCalls: { tavily: 0, openrouter: 0, scraping: 0 },
      dataMetrics: { urlsScraped: 0, totalContent: 0, analysisDepth: 0, queriesGenerated: 0 }
    },
    scrapingProgress: { total: 0, completed: 0, failed: 0, inProgress: [] },
    analysisProgress: { 
      stage1Complete: false, 
      stage2Complete: false, 
      stage3Complete: false, 
      currentStage: 1 
    }
  };
}

export function addEnhancedLog(
  agent: string,
  phase: string,
  message: string,
  level: 'debug' | 'info' | 'warn' | 'error' = 'info'
): Partial<EnhancedResearchStateType> {
  return {
    logs: [{
      agent,
      phase,
      message,
      timestamp: Date.now(),
      level,
    }],
  };
}

export function addSupervisorDecision(
  phase: string,
  decision: 'proceed' | 'retry' | 'escalate' | 'pivot' | 'abort',
  reasoning: string,
  qualityScore: number,
  recommendations: string[] = [],
  nextAction: string = ''
): Partial<EnhancedResearchStateType> {
  return {
    supervisorDecisions: [{
      phase,
      decision,
      reasoning,
      recommendations,
      qualityScore,
      nextAction,
      timestamp: Date.now(),
    }],
  };
}

export function updatePhaseMetrics(
  state: EnhancedResearchStateType,
  phase: string,
  isStart: boolean = true
): Partial<EnhancedResearchStateType> {
  const currentMetrics = state.performanceMetrics;
  const phaseMetrics = { ...currentMetrics.phaseMetrics };
  
  if (isStart) {
    phaseMetrics[phase] = {
      startTime: Date.now(),
      retries: 0,
    };
  } else {
    const existingPhase = phaseMetrics[phase];
    if (existingPhase) {
      const endTime = Date.now();
      phaseMetrics[phase] = {
        ...existingPhase,
        endTime,
        duration: endTime - existingPhase.startTime,
      };
    }
  }

  return {
    performanceMetrics: {
      ...currentMetrics,
      phaseMetrics,
    },
  };
}

export function incrementApiCall(
  apiType: 'tavily' | 'openrouter' | 'scraping'
): Partial<EnhancedResearchStateType> {
  return {
    performanceMetrics: {
      apiCalls: {
        [apiType]: 1
      }
    } as any
  };
}