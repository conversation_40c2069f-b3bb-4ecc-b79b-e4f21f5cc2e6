import { Annotation } from "@langchain/langgraph";

// Research Data Interfaces
export interface ResearchData {
  url: string;
  title: string;
  content: string;
  summary: string;
  statistics: string[];
  keyPoints: string[];
  source: string;
  timestamp: number;
  quality: number;
}

export interface CompetitorData {
  url: string;
  title: string;
  wordCount: number;
  structure: string[];
  strengths: string[];
  gaps: string[];
  keyPoints: string[];
  seoScore: number;
}

export interface ContentDraft {
  title: string;
  content: string;
  wordCount: number;
  seoScore: number;
  readabilityScore: number;
  humanizationScore: number;
  sections: string[];
}

export interface QualityMetrics {
  overall: number;
  research: number;
  writing: number;
  seo: number;
  readability: number;
  humanization: number;
  factAccuracy: number;
  issues: string[];
  recommendations: string[];
}

export interface WorkflowMetrics {
  startTime: number;
  endTime?: number;
  duration?: number;
  phaseMetrics: Record<string, { startTime: number; endTime?: number; duration?: number; }>;
  retryCount: number;
  errors: Array<{ phase: string; error: string; timestamp: number; }>;
}

// Agent Phase Definitions
export type AgentPhase = 
  | 'initialization'
  | 'research'
  | 'competition_analysis' 
  | 'content_generation'
  | 'quality_assessment'
  | 'human_review'
  | 'completion'
  | 'failure';

export type WorkflowType = 
  | 'standard'
  | 'research_heavy'
  | 'competition_focused'
  | 'quick_turnaround'
  | 'premium_quality';

export type ContentType = 
  | 'article'
  | 'blog_post'
  | 'guide'
  | 'tutorial'
  | 'review'
  | 'comparison'
  | 'listicle'
  | 'news'
  | 'opinion';

export type QualityGate = 
  | 'passed'
  | 'retry_research'
  | 'retry_writing'
  | 'retry_quality'
  | 'escalate_human'
  | 'failed';

// LangGraph State Schema
export const GraphState = Annotation.Root({
  // Input Configuration
  topic: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  articleType: Annotation<ContentType>({
    reducer: (x, y) => y ?? x,
  }),
  
  targetAudience: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  tone: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  targetWordCount: Annotation<number>({
    reducer: (x, y) => y ?? x,
  }),
  
  // Workflow Management
  currentPhase: Annotation<AgentPhase>({
    reducer: (x, y) => y ?? x,
  }),
  
  workflowType: Annotation<WorkflowType>({
    reducer: (x, y) => y ?? x,
  }),
  
  // Research Data
  researchData: Annotation<ResearchData[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  searchQueries: Annotation<string[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // Competition Analysis
  competitorAnalysis: Annotation<CompetitorData[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // Content Generation
  contentDraft: Annotation<ContentDraft>({
    reducer: (x, y) => y ?? x,
  }),
  
  previousDrafts: Annotation<ContentDraft[]>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // Quality Management
  qualityMetrics: Annotation<QualityMetrics>({
    reducer: (x, y) => y ?? x,
  }),
  
  qualityGate: Annotation<QualityGate>({
    reducer: (x, y) => y ?? x,
  }),
  
  qualityThreshold: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 80,
  }),
  
  // Retry and Error Handling
  retryCount: Annotation<number>({
    reducer: (x, y) => (y ?? 0) + (x ?? 0),
  }),
  
  maxRetries: Annotation<number>({
    reducer: (x, y) => y ?? x ?? 3,
  }),
  
  errors: Annotation<Array<{ phase: string; error: string; timestamp: number; }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // Human Review
  humanReviewRequired: Annotation<boolean>({
    reducer: (x, y) => y ?? x ?? false,
  }),
  
  humanFeedback: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  
  // Workflow Metrics
  workflowMetrics: Annotation<WorkflowMetrics>({
    reducer: (x, y) => y ?? x,
  }),
  
  // Agent Communication
  logs: Annotation<Array<{ agent: string; message: string; timestamp: number; level: 'info' | 'warn' | 'error'; }>>({
    reducer: (x, y) => {
      if (!y) return x ?? [];
      return [...(x ?? []), ...y];
    },
  }),
  
  // Final Output
  finalContent: Annotation<ContentDraft>({
    reducer: (x, y) => y ?? x,
  }),
  
  success: Annotation<boolean>({
    reducer: (x, y) => y ?? x ?? false,
  }),
});

export type GraphStateType = typeof GraphState.State;

// Helper Functions
export function createInitialState(
  topic: string,
  articleType: ContentType,
  options: {
    targetAudience?: string;
    tone?: string;
    targetWordCount?: number;
    workflowType?: WorkflowType;
    qualityThreshold?: number;
    maxRetries?: number;
  } = {}
): Partial<GraphStateType> {
  return {
    topic,
    articleType,
    targetAudience: options.targetAudience || 'general audience',
    tone: options.tone || 'professional',
    targetWordCount: options.targetWordCount || 1500,
    workflowType: options.workflowType || 'standard',
    qualityThreshold: options.qualityThreshold || 80,
    maxRetries: options.maxRetries || 3,
    currentPhase: 'initialization',
    retryCount: 0,
    researchData: [],
    competitorAnalysis: [],
    searchQueries: [],
    previousDrafts: [],
    errors: [],
    logs: [],
    humanReviewRequired: false,
    success: false,
    workflowMetrics: {
      startTime: Date.now(),
      phaseMetrics: {},
      retryCount: 0,
      errors: [],
    },
  };
}

export function addLog(
  state: GraphStateType,
  agent: string,
  message: string,
  level: 'info' | 'warn' | 'error' = 'info'
): Partial<GraphStateType> {
  return {
    logs: [{
      agent,
      message,
      timestamp: Date.now(),
      level,
    }],
  };
}

export function addError(
  state: GraphStateType,
  phase: string,
  error: string
): Partial<GraphStateType> {
  return {
    errors: [{
      phase,
      error,
      timestamp: Date.now(),
    }],
  };
}

export function updatePhaseMetrics(
  state: GraphStateType,
  phase: AgentPhase,
  isStart: boolean = true
): Partial<GraphStateType> {
  const currentMetrics = state.workflowMetrics || {
    startTime: Date.now(),
    phaseMetrics: {},
    retryCount: 0,
    errors: [],
  };

  const phaseMetrics = { ...currentMetrics.phaseMetrics };
  
  if (isStart) {
    phaseMetrics[phase] = {
      startTime: Date.now(),
    };
  } else {
    const existingPhase = phaseMetrics[phase];
    if (existingPhase) {
      const endTime = Date.now();
      phaseMetrics[phase] = {
        ...existingPhase,
        endTime,
        duration: endTime - existingPhase.startTime,
      };
    }
  }

  return {
    workflowMetrics: {
      ...currentMetrics,
      phaseMetrics,
    },
  };
}