/**
 * Enhanced Invincible Agent for 2025
 * Integrates SEO, AEO (Answer Engine Optimization), and GEO (Generative Engine Optimization)
 * Optimized for AI-driven search engines and LLM platforms
 */

import { InvincibleAgent, InvincibleResult } from './invincible-agent';
import { TaskContext, GeneratedContent } from './types';
import { AEOOptimizer, AEOAnalysis } from './aeo-optimizer';
import { GEOOptimizer, GEOAnalysis } from './geo-optimizer';
import { EnhancedSchemaGenerator, AIOptimizedSchema } from './enhanced-schema-generator';
import { getOptimalPatternForTopic, EnhancedArticlePattern, CONTENT_GENERATION_STRATEGIES_2025 } from '../enhanced-article-patterns-2025';
import { KnowledgeBase } from '../knowledge-base';

export interface Enhanced2025Result extends InvincibleResult {
  aeoAnalysis?: AEOAnalysis;
  geoAnalysis?: GEOAnalysis;
  enhancedPattern?: EnhancedArticlePattern;
  schemaMarkup?: AIOptimizedSchema;
  optimizationScores?: OptimizationScores;
  aiCompatibilityReport?: AICompatibilityReport;
  contentStrategy2025?: ContentStrategy2025;
}

export interface OptimizationScores {
  seoScore: number;
  aeoScore: number;
  geoScore: number;
  aiVisibilityScore: number;
  overallScore: number;
  recommendations: string[];
}

export interface AICompatibilityReport {
  voiceSearchCompatibility: number;
  llmCitationPotential: number;
  featuredSnippetOptimization: number;
  aiReadabilityScore: number;
  semanticRichness: number;
  contextualClarity: number;
  improvements: string[];
}

export interface ContentStrategy2025 {
  primaryOptimizationFocus: 'seo' | 'aeo' | 'geo' | 'balanced';
  contentStructure: string[];
  aiOptimizations: string[];
  targetPlatforms: string[];
  performancePredictions: PerformancePrediction[];
}

export interface PerformancePrediction {
  platform: string;
  visibilityScore: number;
  citationPotential: number;
  engagementPredict: number;
  recommendations: string[];
}

export class EnhancedInvincibleAgent2025 extends InvincibleAgent {
  private aeoOptimizer: AEOOptimizer;
  private geoOptimizer: GEOOptimizer;
  private schemaGenerator: EnhancedSchemaGenerator;

  constructor(config?: any) {
    super(config);
    this.aeoOptimizer = new AEOOptimizer();
    this.geoOptimizer = new GEOOptimizer();
    this.schemaGenerator = new EnhancedSchemaGenerator();
  }

  /**
   * Execute enhanced content generation with 2025 optimizations
   */
  async execute(context: TaskContext): Promise<Enhanced2025Result> {
    const startTime = Date.now();
    this.log('🚀 Enhanced Invincible Agent 2025 - Starting execution');

    try {
      // PHASE 1: Enhanced Pattern Analysis
      this.log('📊 PHASE 1: Enhanced Pattern Analysis for 2025');
      const enhancedPattern = getOptimalPatternForTopic(context.topic);
      this.log(`Selected pattern: ${enhancedPattern.name} (Category: ${enhancedPattern.category})`);

      // PHASE 2: AI Optimization Analysis
      this.log('🤖 PHASE 2: AI Optimization Analysis (AEO + GEO)');
      const [aeoAnalysis, geoAnalysis] = await Promise.all([
        this.aeoOptimizer.analyzeTopic(context.topic, context.targetAudience || 'general'),
        this.geoOptimizer.analyzeTopic(context.topic, context.targetAudience || 'general')
      ]);

      this.log(`AEO Compatibility: ${(aeoAnalysis.optimizations.aiCompatibilityScore * 100).toFixed(1)}%`);
      this.log(`GEO Score: ${(geoAnalysis.optimizations.geoScore * 100).toFixed(1)}%`);

      // PHASE 3: Enhanced Content Strategy
      this.log('📋 PHASE 3: Enhanced Content Strategy Development');
      const contentStrategy2025 = this.developContentStrategy2025(
        context, 
        enhancedPattern, 
        aeoAnalysis, 
        geoAnalysis
      );

      // PHASE 4: Traditional Invincible Processing (Enhanced)
      this.log('⚡ PHASE 4: Enhanced Traditional Processing');
      const traditionalResult = await super.execute({
        ...context,
        customInstructions: this.enhanceCustomInstructions(
          context.customInstructions,
          enhancedPattern,
          aeoAnalysis,
          geoAnalysis,
          contentStrategy2025
        )
      });

      // PHASE 5: AI-Optimized Content Enhancement
      this.log('✨ PHASE 5: AI-Optimized Content Enhancement');
      const enhancedContent = await this.enhanceContentWith2025Optimizations(
        traditionalResult.article!,
        context,
        enhancedPattern,
        aeoAnalysis,
        geoAnalysis
      );

      // PHASE 6: Schema Markup Generation
      this.log('🏗️ PHASE 6: Enhanced Schema Markup Generation');
      const schemaMarkup = this.schemaGenerator.generateSchemaMarkup(
        context.topic,
        enhancedContent.content,
        enhancedPattern
      );

      // PHASE 7: Performance Analysis
      this.log('📈 PHASE 7: Performance Analysis & Scoring');
      const optimizationScores = this.calculateOptimizationScores(
        enhancedContent,
        enhancedPattern,
        aeoAnalysis,
        geoAnalysis
      );

      const aiCompatibilityReport = this.generateAICompatibilityReport(
        enhancedContent,
        aeoAnalysis,
        geoAnalysis
      );

      const executionTime = Date.now() - startTime;
      this.log(`✅ Enhanced Invincible Agent 2025 completed in ${executionTime}ms`);

      return {
        ...traditionalResult,
        article: enhancedContent,
        aeoAnalysis,
        geoAnalysis,
        enhancedPattern,
        schemaMarkup,
        optimizationScores,
        aiCompatibilityReport,
        contentStrategy2025,
        executionTime
      };

    } catch (error) {
      this.log(`❌ Error in Enhanced Invincible Agent 2025: ${error}`);
      throw error;
    }
  }

  /**
   * Develop comprehensive content strategy for 2025
   */
  private developContentStrategy2025(
    context: TaskContext,
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): ContentStrategy2025 {
    const primaryFocus = this.determinePrimaryOptimizationFocus(pattern, aeoAnalysis, geoAnalysis);
    
    return {
      primaryOptimizationFocus: primaryFocus,
      contentStructure: this.generateEnhancedContentStructure(pattern, aeoAnalysis, geoAnalysis),
      aiOptimizations: this.compileAIOptimizations(aeoAnalysis, geoAnalysis),
      targetPlatforms: this.identifyTargetPlatforms(context.topic, pattern),
      performancePredictions: this.generatePerformancePredictions(pattern, aeoAnalysis, geoAnalysis)
    };
  }

  /**
   * Enhance custom instructions with 2025 optimizations
   */
  private enhanceCustomInstructions(
    originalInstructions: string | undefined,
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis,
    strategy: ContentStrategy2025
  ): string {
    const baseInstructions = originalInstructions || '';
    
    const enhancedInstructions = `
${baseInstructions}

🎯 **2025 OPTIMIZATION REQUIREMENTS:**

**PRIMARY FOCUS:** ${strategy.primaryOptimizationFocus.toUpperCase()} Optimization

**PATTERN OPTIMIZATION (${pattern.name}):**
${pattern.seoOptimization.headingStructure.map(h => `- ${h}`).join('\n')}

**AEO REQUIREMENTS (Voice Search & AI Assistants):**
${aeoAnalysis.optimizations.directAnswers.slice(0, 3).map(da => `- Q: ${da.question}\n  A: ${da.answer}`).join('\n')}

**GEO REQUIREMENTS (LLM Platforms):**
- Structure for easy citation and extraction
- Include quotable statements: ${geoAnalysis.optimizations.citationOptimization.quotableStatements.slice(0, 2).map(qs => `"${qs.statement}"`).join(', ')}
- Use modular, self-contained sections
- Optimize for semantic richness and contextual clarity

**AI COMPATIBILITY REQUIREMENTS:**
- Target AI Readability Score: ${(geoAnalysis.aiReadability * 100).toFixed(0)}%
- Voice Search Compatibility: ${(aeoAnalysis.voiceSearchCompatibility * 100).toFixed(0)}%
- Citation Potential: ${(geoAnalysis.citationPotential * 100).toFixed(0)}%

**CONTENT STRUCTURE:**
${strategy.contentStructure.map(cs => `- ${cs}`).join('\n')}

**AI OPTIMIZATIONS:**
${strategy.aiOptimizations.map(ao => `- ${ao}`).join('\n')}

**CRITICAL:** Ensure content works seamlessly across traditional search, voice assistants, and LLM platforms while maintaining human readability and engagement.
`;

    return enhancedInstructions;
  }

  /**
   * Enhance content with 2025 AI optimizations
   */
  private async enhanceContentWith2025Optimizations(
    originalContent: GeneratedContent,
    context: TaskContext,
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): Promise<GeneratedContent> {
    this.log('🔧 Applying 2025 AI optimizations to content');

    // Apply AEO optimizations
    const aeoEnhanced = await this.applyAEOOptimizations(originalContent, aeoAnalysis);
    
    // Apply GEO optimizations  
    const geoEnhanced = await this.applyGEOOptimizations(aeoEnhanced, geoAnalysis);
    
    // Apply enhanced pattern optimizations
    const patternEnhanced = await this.applyPatternOptimizations(geoEnhanced, pattern);
    
    // Final AI compatibility check and adjustments
    const finalContent = await this.finalizeAICompatibility(patternEnhanced, context, pattern);

    return finalContent;
  }

  /**
   * Apply AEO (Answer Engine Optimization) to content
   */
  private async applyAEOOptimizations(
    content: GeneratedContent,
    aeoAnalysis: AEOAnalysis
  ): Promise<GeneratedContent> {
    // Add direct answers within first 50 words
    let enhancedContent = content.content;
    
    // Insert FAQ section optimized for voice search
    const faqSection = this.generateAEOFAQSection(aeoAnalysis);
    enhancedContent += `\n\n## Frequently Asked Questions\n\n${faqSection}`;
    
    // Optimize meta description for conversational queries
    const enhancedMetaDescription = aeoAnalysis.optimizations.featuredSnippetOptimization.metaDescription;
    
    return {
      ...content,
      content: enhancedContent,
      metaDescription: enhancedMetaDescription
    };
  }

  /**
   * Apply GEO (Generative Engine Optimization) to content
   */
  private async applyGEOOptimizations(
    content: GeneratedContent,
    geoAnalysis: GEOAnalysis
  ): Promise<GeneratedContent> {
    let enhancedContent = content.content;
    
    // Add quotable statements throughout content
    const quotableStatements = geoAnalysis.optimizations.citationOptimization.quotableStatements;
    quotableStatements.forEach(statement => {
      const quotableBlock = `\n\n> "${statement.statement}"\n\n`;
      if (statement.placement === 'introduction' && !enhancedContent.includes(quotableBlock)) {
        const firstParagraphEnd = enhancedContent.indexOf('\n\n');
        if (firstParagraphEnd > 0) {
          enhancedContent = 
            enhancedContent.slice(0, firstParagraphEnd) + 
            quotableBlock + 
            enhancedContent.slice(firstParagraphEnd);
        }
      }
    });

    // Add expert opinions and data points
    const expertSection = this.generateGEOExpertSection(geoAnalysis);
    enhancedContent += `\n\n## Expert Insights and Data\n\n${expertSection}`;

    return {
      ...content,
      content: enhancedContent
    };
  }

  /**
   * Apply enhanced pattern optimizations
   */
  private async applyPatternOptimizations(
    content: GeneratedContent,
    pattern: EnhancedArticlePattern
  ): Promise<GeneratedContent> {
    // Apply pattern-specific structural optimizations
    let enhancedContent = content.content;
    
    // Ensure proper heading structure
    enhancedContent = this.optimizeHeadingStructure(enhancedContent, pattern);
    
    // Add pattern-specific elements
    if (pattern.contentStructure.scanningOptimization.length > 0) {
      enhancedContent = this.applyScanningOptimizations(enhancedContent, pattern);
    }

    // Calculate enhanced metrics
    const wordCount = enhancedContent.split(' ').length;
    const readabilityScore = this.calculateEnhancedReadabilityScore(enhancedContent);

    return {
      ...content,
      content: enhancedContent,
      wordCount,
      readabilityScore
    };
  }

  /**
   * Finalize AI compatibility
   */
  private async finalizeAICompatibility(
    content: GeneratedContent,
    context: TaskContext,
    pattern: EnhancedArticlePattern
  ): Promise<GeneratedContent> {
    // Ensure content meets all 2025 standards
    let finalContent = content.content;
    
    // Add semantic enrichment
    finalContent = this.addSemanticEnrichment(finalContent, context.topic);
    
    // Optimize for chunking and embeddings
    finalContent = this.optimizeForChunking(finalContent);
    
    // Final quality check
    const qualityScore = this.calculateQualityScore(finalContent, pattern);

    return {
      ...content,
      content: finalContent
    };
  }

  /**
   * Helper methods for content optimization
   */
  private generateAEOFAQSection(aeoAnalysis: AEOAnalysis): string {
    return aeoAnalysis.optimizations.questionBasedStructure
      .slice(0, 5)
      .map(qa => `### ${qa.question}\n\n${qa.answer}\n`)
      .join('\n');
  }

  private generateGEOExpertSection(geoAnalysis: GEOAnalysis): string {
    const expertOpinions = geoAnalysis.optimizations.citationOptimization.expertOpinions;
    const dataPoints = geoAnalysis.optimizations.citationOptimization.dataPoints;
    
    let section = '';
    
    if (expertOpinions.length > 0) {
      section += '### Expert Opinions\n\n';
      expertOpinions.slice(0, 2).forEach(opinion => {
        section += `**${opinion.expert}** (${opinion.credentials}): "${opinion.opinion}"\n\n`;
      });
    }
    
    if (dataPoints.length > 0) {
      section += '### Key Data Points\n\n';
      dataPoints.slice(0, 3).forEach(dp => {
        section += `- **${dp.metric}**: ${dp.value} (Source: ${dp.source})\n`;
      });
    }
    
    return section;
  }

  private optimizeHeadingStructure(content: string, pattern: EnhancedArticlePattern): string {
    // Implementation would optimize heading hierarchy based on pattern
    return content;
  }

  private applyScanningOptimizations(content: string, pattern: EnhancedArticlePattern): string {
    // Implementation would apply scanning optimizations like bullet points, bold text, etc.
    return content;
  }

  private addSemanticEnrichment(content: string, topic: string): string {
    // Add semantic keywords and related terms
    return content;
  }

  private optimizeForChunking(content: string): string {
    // Optimize content for AI chunking and embedding
    return content;
  }

  /**
   * Calculation and analysis methods
   */
  private determinePrimaryOptimizationFocus(
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): 'seo' | 'aeo' | 'geo' | 'balanced' {
    const seoScore = pattern.performance.seoScore;
    const aeoScore = pattern.performance.aeoScore;
    const geoScore = pattern.performance.geoScore;
    
    const maxScore = Math.max(seoScore, aeoScore, geoScore);
    const scoreDifference = maxScore - Math.min(seoScore, aeoScore, geoScore);
    
    if (scoreDifference < 10) return 'balanced';
    if (maxScore === aeoScore) return 'aeo';
    if (maxScore === geoScore) return 'geo';
    return 'seo';
  }

  private generateEnhancedContentStructure(
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): string[] {
    return [
      ...pattern.contentStructure.introduction,
      'Direct answers for voice search (AEO)',
      'Quotable statements for citation (GEO)',
      ...pattern.contentStructure.mainBody,
      'Expert insights and data points',
      'FAQ section for AI assistants',
      ...pattern.contentStructure.conclusion,
      'Schema markup optimization'
    ];
  }

  private compileAIOptimizations(aeoAnalysis: AEOAnalysis, geoAnalysis: GEOAnalysis): string[] {
    return [
      `Voice search compatibility: ${(aeoAnalysis.voiceSearchCompatibility * 100).toFixed(0)}%`,
      `AI readability score: ${(geoAnalysis.aiReadability * 100).toFixed(0)}%`,
      `Citation potential: ${(geoAnalysis.citationPotential * 100).toFixed(0)}%`,
      'Question-based structure for AEO',
      'Modular sections for GEO',
      'Semantic enrichment for LLM understanding',
      'Schema markup for AI crawlers'
    ];
  }

  private identifyTargetPlatforms(topic: string, pattern: EnhancedArticlePattern): string[] {
    const platforms = ['Google Search', 'Google AI Overviews'];
    
    if (pattern.performance.aeoScore > 80) {
      platforms.push('Google Assistant', 'Siri', 'Alexa');
    }
    
    if (pattern.performance.geoScore > 80) {
      platforms.push('ChatGPT', 'Claude', 'Perplexity', 'Google Gemini');
    }
    
    return platforms;
  }

  private generatePerformancePredictions(
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): PerformancePrediction[] {
    return [
      {
        platform: 'Google Search',
        visibilityScore: pattern.performance.seoScore / 100,
        citationPotential: 0.7,
        engagementPredict: pattern.performance.engagementRate,
        recommendations: ['Optimize for featured snippets', 'Improve technical SEO']
      },
      {
        platform: 'AI Assistants (AEO)',
        visibilityScore: aeoAnalysis.optimizations.aiCompatibilityScore,
        citationPotential: aeoAnalysis.voiceSearchCompatibility,
        engagementPredict: 0.8,
        recommendations: ['Add more conversational elements', 'Optimize question-answer structure']
      },
      {
        platform: 'LLM Platforms (GEO)',
        visibilityScore: geoAnalysis.optimizations.geoScore,
        citationPotential: geoAnalysis.citationPotential,
        engagementPredict: 0.75,
        recommendations: ['Increase quotable statements', 'Add more expert opinions']
      }
    ];
  }

  private calculateOptimizationScores(
    content: GeneratedContent,
    pattern: EnhancedArticlePattern,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): OptimizationScores {
    const seoScore = pattern.performance.seoScore;
    const aeoScore = aeoAnalysis.optimizations.aiCompatibilityScore * 100;
    const geoScore = geoAnalysis.optimizations.geoScore * 100;
    const aiVisibilityScore = (aeoScore + geoScore) / 2;
    const overallScore = (seoScore + aeoScore + geoScore) / 3;

    return {
      seoScore,
      aeoScore,
      geoScore,
      aiVisibilityScore,
      overallScore,
      recommendations: this.generateOptimizationRecommendations(seoScore, aeoScore, geoScore)
    };
  }

  private generateAICompatibilityReport(
    content: GeneratedContent,
    aeoAnalysis: AEOAnalysis,
    geoAnalysis: GEOAnalysis
  ): AICompatibilityReport {
    return {
      voiceSearchCompatibility: aeoAnalysis.voiceSearchCompatibility,
      llmCitationPotential: geoAnalysis.citationPotential,
      featuredSnippetOptimization: 0.8, // Calculate based on content structure
      aiReadabilityScore: geoAnalysis.aiReadability,
      semanticRichness: 0.75, // Calculate based on content analysis
      contextualClarity: 0.85, // Calculate based on content clarity
      improvements: this.generateImprovementSuggestions(aeoAnalysis, geoAnalysis)
    };
  }

  private generateOptimizationRecommendations(seoScore: number, aeoScore: number, geoScore: number): string[] {
    const recommendations = [];
    
    if (seoScore < 80) recommendations.push('Improve traditional SEO elements (keywords, meta tags, structure)');
    if (aeoScore < 80) recommendations.push('Enhance voice search optimization and direct answers');
    if (geoScore < 80) recommendations.push('Add more quotable content and expert citations');
    
    recommendations.push('Continue monitoring AI platform performance');
    recommendations.push('Regular content updates for freshness');
    
    return recommendations;
  }

  private generateImprovementSuggestions(aeoAnalysis: AEOAnalysis, geoAnalysis: GEOAnalysis): string[] {
    const suggestions = [];
    
    if (aeoAnalysis.voiceSearchCompatibility < 0.8) {
      suggestions.push('Add more conversational language patterns');
    }
    
    if (geoAnalysis.citationPotential < 0.8) {
      suggestions.push('Include more data points and expert opinions');
    }
    
    suggestions.push('Optimize content chunking for better AI processing');
    suggestions.push('Add more semantic keywords and related terms');
    
    return suggestions;
  }

  /**
   * Quality and compatibility scoring
   */
  private calculateEnhancedReadabilityScore(content: string): number {
    // Simple readability calculation
    const words = content.split(' ').length;
    const sentences = content.split(/[.!?]+/).length;
    const avgWordsPerSentence = words / sentences;
    
    if (avgWordsPerSentence < 15) return 0.9;
    if (avgWordsPerSentence < 20) return 0.7;
    return 0.5;
  }

  private calculateQualityScore(content: string, pattern: EnhancedArticlePattern): number {
    // Calculate overall quality score based on multiple factors
    const wordCount = content.split(' ').length;
    const targetRange = pattern.contentStructure.wordCountRange;
    
    const lengthScore = wordCount >= targetRange[0] && wordCount <= targetRange[1] ? 1.0 : 0.7;
    const structureScore = 0.8; // Would analyze actual structure
    const contentScore = 0.85; // Would analyze content quality
    
    return (lengthScore + structureScore + contentScore) / 3;
  }

  private calculateAICompatibilityScore(content: string, pattern: EnhancedArticlePattern): number {
    // Calculate AI compatibility based on various factors
    const readability = this.calculateEnhancedReadabilityScore(content);
    const structure = 0.8; // Would analyze structure compatibility
    const semantic = 0.75; // Would analyze semantic richness
    
    return (readability + structure + semantic) / 3;
  }
} 