/**
 * Enhanced Writing System for Invincible Agent
 * 
 * A sophisticated writing component that:
 * - Analyzes competition with deep writing style insights
 * - Adapts to every niche with specific patterns
 * - Handles all article types with tailored structures
 * - Understands and applies user-requested tones
 * - Targets individual users by default (not enterprises)
 * - Produces SEO-optimized content that ranks immediately
 */

import { OpenRouterService } from '../openrouter';

export interface WritingContext {
  topic: string;
  niche: string;
  articleType: string;
  targetTone?: string;
  targetAudience?: string;
  wordCount: number;
  competitorContent?: Array<{ content: string; url: string }>;
  researchData?: any[];
  userPreferences?: {
    formalityLevel?: 'casual' | 'professional' | 'academic';
    technicalDepth?: 'beginner' | 'intermediate' | 'expert';
    emotionalTone?: 'neutral' | 'enthusiastic' | 'empathetic' | 'authoritative';
  };
}

export interface WritingAnalysis {
  competitiveInsights: {
    topPerformingElements: string[];
    writingPatterns: any;
    engagementTechniques: string[];
    weaknesses: string[];
  };
  nicheRequirements: {
    essentialElements: string[];
    vocabularyStyle: any;
    structuralPatterns: string[];
    proofRequirements: string[];
  };
  articleTypeStructure: {
    requiredSections: string[];
    optimalFlow: string[];
    keyComponents: any;
    formatRequirements: string[];
  };
  toneAnalysis: {
    detectedTone: string;
    toneElements: string[];
    voiceCharacteristics: string[];
    languageStyle: any;
  };
  audienceProfile: {
    primaryAudience: string;
    knowledgeLevel: string;
    interests: string[];
    painPoints: string[];
  };
}

export interface WritingOutput {
  content: string;
  title: string;
  metaDescription: string;
  writingAnalysis: WritingAnalysis;
  seoScore: number;
  readabilityScore: number;
  uniquenessFactors: string[];
}

export class EnhancedWritingSystem {
  private openRouterService: OpenRouterService;
  
  constructor() {
    this.openRouterService = new OpenRouterService({
      model: 'moonshotai/kimi-k2',
      temperature: 0.7,
      maxTokens: 8000
    });
  }

  /**
   * Get current date context for content relevance
   */
  private getCurrentDateContext(): string {
    const now = new Date();
    const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 
                       'July', 'August', 'September', 'October', 'November', 'December'];
    return `${monthNames[now.getMonth()]} ${now.getFullYear()}`;
  }

  /**
   * Main method to generate superior content
   */
  async generateSuperiorContent(context: WritingContext): Promise<WritingOutput> {
    console.log('🚀 Enhanced Writing System: Starting superior content generation');
    
    // Phase 1: Deep Competitive & Writing Analysis
    const writingAnalysis = await this.performComprehensiveWritingAnalysis(context);
    
    // Phase 2: Generate Pattern-Aware Content
    const generatedContent = await this.generatePatternAwareContent(context, writingAnalysis);
    
    // Phase 3: Quality Assurance & Optimization
    const optimizedContent = await this.performQualityOptimization(generatedContent, context, writingAnalysis);
    
    return optimizedContent;
  }

  /**
   * Phase 1: Comprehensive Writing Analysis
   */
  private async performComprehensiveWritingAnalysis(context: WritingContext): Promise<WritingAnalysis> {
    console.log('📊 Phase 1: Performing comprehensive writing analysis');
    
    const analysisPrompt = `You are an expert content analyst specializing in writing style, competitive analysis, and SEO optimization. Perform a comprehensive analysis for creating superior content.

📅 **Current Context:** ${this.getCurrentDateContext()}

🎯 **Content Requirements:**
- Topic: "${context.topic}"
- Niche: ${context.niche}
- Article Type: ${context.articleType}
- Target Tone: ${context.targetTone || 'professional yet approachable'}
- Target Audience: ${context.targetAudience || 'individual users and enthusiasts (NOT enterprises)'}
- Word Count: ${context.wordCount}

📊 **Competitor Content Analysis:**
${context.competitorContent ? context.competitorContent.map((comp, idx) => `
**Competitor ${idx + 1}:**
URL: ${comp.url}
Content Preview: ${comp.content.substring(0, 1500)}...
`).join('\n') : 'No competitor content provided'}

🔍 **COMPREHENSIVE ANALYSIS REQUIRED:**

**1. COMPETITIVE WRITING INSIGHTS:**
- Identify the TOP 5 writing techniques that make competitors rank well
- Extract engagement patterns that keep readers on page
- Find content gaps and weaknesses to exploit
- Analyze their hook strategies, transitions, and conclusions
- Identify what makes their content shareable and linkable

**2. NICHE-SPECIFIC REQUIREMENTS FOR ${context.niche.toUpperCase()}:**
- Essential elements EVERY ${context.niche} article must have
- Vocabulary and terminology specific to ${context.niche}
- Structural patterns that work best in ${context.niche}
- Types of proof/evidence ${context.niche} readers expect
- Emotional triggers and trust signals for ${context.niche}

**3. ARTICLE TYPE STRUCTURE FOR ${context.articleType.toUpperCase()}:**
${this.getArticleTypeAnalysisPrompt(context.articleType)}

**4. TONE & VOICE ANALYSIS:**
- Analyze the requested tone: "${context.targetTone || 'professional yet approachable'}"
- Define specific language patterns for this tone
- Identify voice characteristics that resonate
- Determine optimal formality level
- Specify emotional undertones to include

**5. AUDIENCE PROFILE (${context.targetAudience || 'INDIVIDUAL USERS'}):**
- Primary audience characteristics and demographics
- Knowledge level and technical understanding
- Core interests and motivations
- Pain points and challenges they face
- Language preferences and reading patterns

**CRITICAL: Remember this content is for INDIVIDUAL USERS, not enterprises, unless specifically stated.**

Provide your analysis in this JSON format:

{
  "competitiveInsights": {
    "topPerformingElements": ["element1", "element2", ...],
    "writingPatterns": {
      "hooks": ["pattern1", "pattern2"],
      "bodyStructure": ["pattern1", "pattern2"],
      "conclusions": ["pattern1", "pattern2"],
      "transitions": ["pattern1", "pattern2"]
    },
    "engagementTechniques": ["technique1", "technique2", ...],
    "weaknesses": ["weakness1", "weakness2", ...]
  },
  "nicheRequirements": {
    "essentialElements": ["element1", "element2", ...],
    "vocabularyStyle": {
      "mustUseTerms": ["term1", "term2", ...],
      "avoidTerms": ["term1", "term2", ...],
      "toneWords": ["word1", "word2", ...]
    },
    "structuralPatterns": ["pattern1", "pattern2", ...],
    "proofRequirements": ["proof1", "proof2", ...]
  },
  "articleTypeStructure": {
    "requiredSections": ["section1", "section2", ...],
    "optimalFlow": ["step1", "step2", ...],
    "keyComponents": {
      "introduction": "specific requirements",
      "body": "specific structure",
      "conclusion": "specific elements"
    },
    "formatRequirements": ["requirement1", "requirement2", ...]
  },
  "toneAnalysis": {
    "detectedTone": "tone description",
    "toneElements": ["element1", "element2", ...],
    "voiceCharacteristics": ["characteristic1", "characteristic2", ...],
    "languageStyle": {
      "sentenceLength": "short/medium/varied",
      "vocabulary": "simple/moderate/advanced",
      "personalPronouns": "frequent/moderate/minimal",
      "contractions": "yes/no",
      "activeVoice": "percentage"
    }
  },
  "audienceProfile": {
    "primaryAudience": "detailed description",
    "knowledgeLevel": "beginner/intermediate/advanced",
    "interests": ["interest1", "interest2", ...],
    "painPoints": ["pain1", "pain2", ...]
  }
}`;

    try {
      const response = await this.openRouterService.generateContent(
        analysisPrompt,
        undefined, // No custom system prompt
        { temperature: 0.3, maxTokens: 64000 }, // MAXIMUM FREEDOM
        'Writing Analysis with Deep Thinking'
      );

      return this.parseAnalysisResponse(response.response);
    } catch (error) {
      console.error('Analysis failed:', error);
      return this.getFallbackAnalysis(context);
    }
  }

  /**
   * Phase 2: Generate Pattern-Aware Content
   */
  private async generatePatternAwareContent(
    context: WritingContext, 
    analysis: WritingAnalysis
  ): Promise<WritingOutput> {
    console.log('✍️ Phase 2: Generating pattern-aware content');
    
    const generationPrompt = `You are an expert content writer creating a ${context.articleType} article that will definitively rank #1 for "${context.topic}". Use the comprehensive analysis to create superior content.

📅 **Current Date:** ${this.getCurrentDateContext()} - Ensure all information is current and relevant.

🎯 **CONTENT SPECIFICATIONS:**
- Topic: "${context.topic}"
- Niche: ${context.niche}
- Article Type: ${context.articleType}
- Target Word Count: ${context.wordCount} words (MUST be within ±10%)
- Tone: ${context.targetTone || 'professional yet approachable'}
- Target Audience: ${context.targetAudience || 'individual users and enthusiasts'}

📊 **WRITING ANALYSIS INSIGHTS:**
${JSON.stringify(analysis, null, 2)}

📚 **RESEARCH DATA AVAILABLE:**
${context.researchData ? context.researchData.slice(0, 5).map(data => 
  `- ${data.title || 'Research point'}: ${data.keyInsight || data.content?.substring(0, 200)}...`
).join('\n') : 'No additional research data'}

✍️ **CONTENT GENERATION REQUIREMENTS:**

**1. COMPETITIVE SUPERIORITY:**
- Implement ALL top-performing elements identified in analysis
- Use the successful writing patterns from competitors
- Address and fix ALL competitor weaknesses
- Include MORE value than any competitor

**2. NICHE-SPECIFIC EXCELLENCE (${context.niche}):**
${this.getNicheSpecificRequirements(context.niche)}

**3. ARTICLE TYPE MASTERY (${context.articleType}):**
${this.getArticleTypeRequirements(context.articleType)}

**4. TONE & VOICE PERFECTION:**
- Apply the exact tone requested: "${context.targetTone || 'professional yet approachable'}"
- Use language patterns identified in tone analysis
- Maintain consistent voice throughout
- Engage readers with appropriate emotional undertones

**5. AUDIENCE-FIRST APPROACH:**
- Write specifically for ${context.targetAudience || 'individual users'}
- Address their knowledge level appropriately
- Solve their specific pain points
- Use language they understand and appreciate
- AVOID enterprise/business focus unless specifically requested

**6. SEO & RANKING OPTIMIZATION:**
- Natural keyword integration (3-5% density)
- Use semantic LSI keywords throughout
- Optimize headings for featured snippets
- Include FAQ section for People Also Ask
- Structure content for maximum dwell time
- Create link-worthy content sections

**7. ENGAGEMENT MAXIMIZATION:**
- Start with an irresistible hook
- Use proven engagement techniques from analysis
- Include interactive elements (questions, thought exercises)
- Add personal touches and relatable examples
- End with a memorable, actionable conclusion

**8. QUALITY STANDARDS:**
- Every claim backed by data or examples
- No fluff - every sentence adds value
- Clear, scannable formatting
- Smooth transitions between sections
- Conversational yet authoritative tone

**CRITICAL REQUIREMENTS:**
- Word count MUST be ${context.wordCount} words (±10%)
- Target individual users unless specified otherwise
- Include ALL essential elements for ${context.niche} niche
- Follow exact structure for ${context.articleType} type
- Apply requested tone consistently
- Make content immediately rankable

**OUTPUT FORMAT:**
\`\`\`markdown
# [Compelling, SEO-Optimized Title]

**Meta Description:** [155-character description that compels clicks]

[Introduction with irresistible hook - ${Math.round(context.wordCount * 0.1)} words]

## [First Major Section]
[Content following all requirements]

## [Subsequent Sections per Article Type Structure]
[Content with all niche-specific elements]

## [FAQ Section if appropriate]
[Address common questions]

## [Conclusion with Clear CTA]
[Memorable ending - ${Math.round(context.wordCount * 0.08)} words]
\`\`\`

Create the superior content now:`;

    try {
      const response = await this.openRouterService.generateContent(
        generationPrompt,
        undefined, // No custom system prompt
        { temperature: 0.7, maxTokens: 64000 }, // MAXIMUM FREEDOM
        'Enhanced Content Generation'
      );

      return this.parseGeneratedContent(response.response, context, analysis);
    } catch (error) {
      console.error('Content generation failed:', error);
      throw new Error('Failed to generate content');
    }
  }

  /**
   * Phase 3: Quality Optimization
   */
  private async performQualityOptimization(
    content: WritingOutput,
    context: WritingContext,
    analysis: WritingAnalysis
  ): Promise<WritingOutput> {
    console.log('🔧 Phase 3: Performing quality optimization');
    
    const optimizationPrompt = `You are a content optimization expert. Review and enhance this content to ensure it will rank #1 immediately.

**ORIGINAL CONTENT:**
${content.content}

**OPTIMIZATION REQUIREMENTS:**

1. **SEO Enhancement:**
   - Ensure 3-5% keyword density for "${context.topic}"
   - Add LSI keywords naturally
   - Optimize all headings for featured snippets
   - Ensure meta description is compelling

2. **Readability Improvement:**
   - Ensure grade 8-10 reading level
   - Vary sentence lengths (8-20 words)
   - Use transition words (30%+)
   - Add bullet points and lists where helpful

3. **Engagement Boost:**
   - Strengthen the hook if needed
   - Add more personal touches
   - Include relatable examples
   - Ensure every paragraph adds value

4. **Niche & Type Compliance:**
   - Verify all ${context.niche} requirements are met
   - Confirm ${context.articleType} structure is perfect
   - Check tone consistency throughout

5. **Word Count Precision:**
   - Current word count: ${this.countWords(content.content)}
   - Target: ${context.wordCount} words
   - Adjust to be within ±5% of target

**OUTPUT:**
Provide the optimized content in the same markdown format, ensuring it's ready to rank #1 immediately.`;

    try {
      const response = await this.openRouterService.generateContent(
        optimizationPrompt,
        undefined, // No custom system prompt
        { temperature: 0.5, maxTokens: 10000 },
        'Content Optimization'
      );

      const optimizedContent = this.parseOptimizedContent(response.response, content, analysis);
      
      // Calculate final scores
      optimizedContent.seoScore = this.calculateSEOScore(optimizedContent, context);
      optimizedContent.readabilityScore = this.calculateReadabilityScore(optimizedContent.content);
      optimizedContent.uniquenessFactors = this.identifyUniquenessFactors(optimizedContent, analysis);

      console.log(`✅ Content optimized: SEO Score: ${optimizedContent.seoScore}, Readability: ${optimizedContent.readabilityScore}`);
      
      return optimizedContent;
    } catch (error) {
      console.error('Optimization failed:', error);
      // Return original content if optimization fails
      return content;
    }
  }

  /**
   * Get article type specific analysis prompt
   */
  private getArticleTypeAnalysisPrompt(articleType: string): string {
    const prompts: { [key: string]: string } = {
      'comparison': `
- Required comparison elements (tables, pros/cons, side-by-side)
- Decision-making frameworks readers expect
- Scoring/rating systems that work
- Visual comparison needs
- Winner declaration approach`,
      
      'how-to': `
- Step-by-step structure requirements
- Prerequisite information needs
- Visual/diagram descriptions needed
- Common mistakes section requirements
- Tools/materials list format`,
      
      'listicle': `
- Optimal number of list items for engagement
- Item structure and length
- Value proposition for each item
- Ordering strategy (best to worst, chronological, etc.)
- Summary/recap requirements`,
      
      'review': `
- Testing methodology section needs
- Scoring criteria structure
- Pros/cons organization
- Personal experience integration
- Verdict/recommendation format`,
      
      'guide': `
- Comprehensive coverage requirements
- Beginner to advanced progression
- Quick reference sections needed
- Practical examples integration
- Resource list requirements`,
      
      'tutorial': `
- Hands-on exercise structure
- Code/example formatting
- Troubleshooting section needs
- Practice project ideas
- Skill progression tracking`
    };

    return prompts[articleType] || prompts['guide'];
  }

  /**
   * Get niche-specific content requirements
   */
  private getNicheSpecificRequirements(niche: string): string {
    const requirements: { [key: string]: string } = {
      'tech': `
- Include specific version numbers and compatibility info
- Add code snippets or technical examples
- Reference official documentation
- Include performance benchmarks
- Address security considerations
- Mention latest updates and future roadmap`,
      
      'health': `
- Include scientific backing and studies
- Add disclaimers where appropriate
- Reference medical professionals
- Include safety information
- Address common misconceptions
- Provide actionable wellness tips`,
      
      'finance': `
- Include current market data and trends
- Add risk disclaimers
- Reference regulatory requirements
- Include calculation examples
- Address tax implications
- Provide actionable investment insights`,
      
      'lifestyle': `
- Include personal anecdotes and stories
- Add practical tips for daily life
- Reference trending topics
- Include cost considerations
- Address different lifestyles/preferences
- Provide quick wins and long-term strategies`,
      
      'business': `
- Include case studies and examples
- Add ROI calculations
- Reference industry standards
- Include implementation timelines
- Address common challenges
- Provide scalability considerations`,
      
      'education': `
- Include learning objectives
- Add practice exercises
- Reference additional resources
- Include progress milestones
- Address different learning styles
- Provide assessment criteria`
    };

    return requirements[niche.toLowerCase()] || requirements['lifestyle'];
  }

  /**
   * Get article type specific generation requirements
   */
  private getArticleTypeRequirements(articleType: string): string {
    const requirements: { [key: string]: string } = {
      'comparison': `
**MANDATORY COMPARISON ELEMENTS:**
- Create at least 2-3 detailed comparison tables
- Include comprehensive pros/cons for each option
- Add a clear scoring system with criteria
- Provide side-by-side feature analysis
- Include a decision-making framework
- Add "best for" recommendations for different users
- Create a summary winner section`,
      
      'how-to': `
**MANDATORY HOW-TO ELEMENTS:**
- Number each step clearly (Step 1, Step 2, etc.)
- Include time estimates for each step
- Add "What You'll Need" section upfront
- Include troubleshooting for common issues
- Add tips and warnings in callout boxes
- Include before/after or progress images descriptions
- End with "Next Steps" or advanced tips`,
      
      'listicle': `
**MANDATORY LISTICLE ELEMENTS:**
- Use numbered format (1., 2., 3., etc.)
- Make each item scannable with bold subheadings
- Include 7-15 items (optimal for engagement)
- Add brief intro for each item (2-3 sentences)
- Include specific examples or data for each
- Order items strategically (best first or building up)
- Add quick summary or recap at the end`,
      
      'review': `
**MANDATORY REVIEW ELEMENTS:**
- Include "First Impressions" section
- Add detailed "Features & Performance" analysis
- Create pros and cons sections
- Include "Who It's For" and "Who Should Avoid"
- Add pricing and value analysis
- Include alternatives comparison
- End with clear verdict and rating`,
      
      'guide': `
**MANDATORY GUIDE ELEMENTS:**
- Start with "What You'll Learn" overview
- Include comprehensive topic coverage
- Add beginner-friendly explanations
- Include advanced tips separately
- Add real-world examples throughout
- Include common mistakes to avoid
- End with action plan or checklist`,
      
      'tutorial': `
**MANDATORY TUTORIAL ELEMENTS:**
- Include learning objectives upfront
- Add prerequisites clearly
- Use step-by-step instructions
- Include code/examples for each concept
- Add practice exercises
- Include common errors and fixes
- End with project ideas or challenges`
    };

    return requirements[articleType] || requirements['guide'];
  }

  /**
   * Parse analysis response with fallback
   */
  private parseAnalysisResponse(response: string): WritingAnalysis {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('Failed to parse analysis response, using fallback');
    }

    return this.getFallbackAnalysis({ topic: '', niche: 'general', articleType: 'guide', wordCount: 2000 });
  }

  /**
   * Get fallback analysis structure
   */
  private getFallbackAnalysis(context: WritingContext): WritingAnalysis {
    return {
      competitiveInsights: {
        topPerformingElements: ['comprehensive coverage', 'clear structure', 'actionable advice', 'data backing', 'engaging tone'],
        writingPatterns: {
          hooks: ['question hook', 'statistic hook', 'story hook'],
          bodyStructure: ['problem-solution', 'chronological', 'hierarchical'],
          conclusions: ['summary + CTA', 'future outlook', 'personal recommendation'],
          transitions: ['furthermore', 'however', 'in addition', 'consequently']
        },
        engagementTechniques: ['rhetorical questions', 'personal anecdotes', 'interactive elements'],
        weaknesses: ['lack of data', 'poor structure', 'no clear CTA']
      },
      nicheRequirements: {
        essentialElements: this.getDefaultNicheElements(context.niche),
        vocabularyStyle: {
          mustUseTerms: this.getDefaultNicheTerms(context.niche),
          avoidTerms: ['complicated jargon', 'outdated terms'],
          toneWords: ['practical', 'actionable', 'proven']
        },
        structuralPatterns: ['clear hierarchy', 'logical flow', 'scannable sections'],
        proofRequirements: ['statistics', 'case studies', 'expert quotes']
      },
      articleTypeStructure: {
        requiredSections: this.getDefaultArticleSections(context.articleType),
        optimalFlow: ['hook', 'context', 'main content', 'summary', 'CTA'],
        keyComponents: {
          introduction: 'hook + value proposition',
          body: 'structured sections with examples',
          conclusion: 'summary + next steps'
        },
        formatRequirements: ['headings', 'bullet points', 'short paragraphs']
      },
      toneAnalysis: {
        detectedTone: context.targetTone || 'professional yet approachable',
        toneElements: ['conversational', 'authoritative', 'helpful'],
        voiceCharacteristics: ['active voice', 'direct address', 'inclusive language'],
        languageStyle: {
          sentenceLength: 'varied',
          vocabulary: 'moderate',
          personalPronouns: 'frequent',
          contractions: 'yes',
          activeVoice: '80%'
        }
      },
      audienceProfile: {
        primaryAudience: context.targetAudience || 'individual users seeking practical solutions',
        knowledgeLevel: 'intermediate',
        interests: ['practical solutions', 'time-saving tips', 'proven strategies'],
        painPoints: ['information overload', 'lack of clarity', 'need for actionable advice']
      }
    };
  }

  /**
   * Get default niche elements
   */
  private getDefaultNicheElements(niche: string): string[] {
    const elements: { [key: string]: string[] } = {
      'tech': ['code examples', 'technical specs', 'compatibility info', 'performance data'],
      'health': ['scientific backing', 'safety info', 'disclaimers', 'practical tips'],
      'finance': ['calculations', 'risk warnings', 'market data', 'regulations'],
      'lifestyle': ['personal stories', 'practical tips', 'cost breakdown', 'time estimates'],
      'business': ['ROI analysis', 'case studies', 'implementation steps', 'metrics'],
      'education': ['learning objectives', 'exercises', 'assessments', 'resources']
    };

    return elements[niche.toLowerCase()] || elements['lifestyle'];
  }

  /**
   * Get default niche terms
   */
  private getDefaultNicheTerms(niche: string): string[] {
    const terms: { [key: string]: string[] } = {
      'tech': ['API', 'framework', 'deployment', 'optimization', 'integration'],
      'health': ['wellness', 'prevention', 'treatment', 'symptoms', 'recovery'],
      'finance': ['ROI', 'portfolio', 'investment', 'returns', 'risk'],
      'lifestyle': ['routine', 'habits', 'balance', 'mindfulness', 'productivity'],
      'business': ['strategy', 'growth', 'scalability', 'efficiency', 'revenue'],
      'education': ['curriculum', 'assessment', 'pedagogy', 'outcomes', 'skills']
    };

    return terms[niche.toLowerCase()] || terms['lifestyle'];
  }

  /**
   * Get default article sections
   */
  private getDefaultArticleSections(articleType: string): string[] {
    const sections: { [key: string]: string[] } = {
      'comparison': ['overview', 'criteria', 'detailed comparison', 'pros/cons', 'verdict'],
      'how-to': ['introduction', 'requirements', 'steps', 'tips', 'troubleshooting'],
      'listicle': ['introduction', 'list items', 'honorable mentions', 'summary'],
      'review': ['overview', 'features', 'performance', 'pros/cons', 'verdict'],
      'guide': ['introduction', 'basics', 'advanced topics', 'best practices', 'resources'],
      'tutorial': ['objectives', 'prerequisites', 'lessons', 'exercises', 'next steps']
    };

    return sections[articleType] || sections['guide'];
  }

  /**
   * Parse generated content
   */
  private parseGeneratedContent(
    response: string, 
    context: WritingContext,
    analysis: WritingAnalysis
  ): WritingOutput {
    // Extract content from markdown
    const content = response.replace(/```markdown\n?/, '').replace(/```$/, '').trim();
    
    // Extract title
    const titleMatch = content.match(/^#\s+(.+)$/m);
    const title = titleMatch ? titleMatch[1] : `${context.topic}: Complete Guide`;
    
    // Extract meta description
    const metaMatch = content.match(/\*\*Meta Description:\*\*\s*(.+)/);
    const metaDescription = metaMatch ? metaMatch[1] : this.generateMetaDescription(context.topic, content);

    return {
      content,
      title,
      metaDescription,
      writingAnalysis: analysis,
      seoScore: 0, // Will be calculated in optimization phase
      readabilityScore: 0, // Will be calculated in optimization phase
      uniquenessFactors: [] // Will be identified in optimization phase
    };
  }

  /**
   * Parse optimized content
   */
  private parseOptimizedContent(
    response: string,
    originalContent: WritingOutput,
    analysis: WritingAnalysis
  ): WritingOutput {
    try {
      // Extract optimized content
      const content = response.replace(/```markdown\n?/, '').replace(/```$/, '').trim();
      
      // Re-extract title and meta
      const titleMatch = content.match(/^#\s+(.+)$/m);
      const title = titleMatch ? titleMatch[1] : originalContent.title;
      
      const metaMatch = content.match(/\*\*Meta Description:\*\*\s*(.+)/);
      const metaDescription = metaMatch ? metaMatch[1] : originalContent.metaDescription;

      return {
        content,
        title,
        metaDescription,
        writingAnalysis: analysis,
        seoScore: originalContent.seoScore,
        readabilityScore: originalContent.readabilityScore,
        uniquenessFactors: originalContent.uniquenessFactors
      };
    } catch (error) {
      console.warn('Failed to parse optimized content, returning original');
      return originalContent;
    }
  }

  /**
   * Generate meta description fallback
   */
  private generateMetaDescription(topic: string, content: string): string {
    const firstParagraph = content.split('\n\n')[1] || '';
    const cleaned = firstParagraph.replace(/[#*`]/g, '').trim();
    const truncated = cleaned.substring(0, 150);
    return truncated + (cleaned.length > 150 ? '...' : '');
  }

  /**
   * Count words accurately
   */
  private countWords(content: string): number {
    const text = content
      .replace(/```[\s\S]*?```/g, '') // Remove code blocks
      .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // Replace links with text
      .replace(/[#*`_~]/g, '') // Remove markdown formatting
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Calculate SEO score
   */
  private calculateSEOScore(content: WritingOutput, context: WritingContext): number {
    let score = 0;
    const text = content.content.toLowerCase();
    const topic = context.topic.toLowerCase();
    
    // Keyword density (20 points)
    const wordCount = this.countWords(content.content);
    const keywordCount = (text.match(new RegExp(topic, 'g')) || []).length;
    const density = (keywordCount / wordCount) * 100;
    if (density >= 2 && density <= 5) score += 20;
    else if (density >= 1 && density <= 6) score += 10;
    
    // Title optimization (15 points)
    if (content.title.toLowerCase().includes(topic)) score += 15;
    
    // Meta description (15 points)
    if (content.metaDescription.length >= 120 && content.metaDescription.length <= 160) score += 10;
    if (content.metaDescription.toLowerCase().includes(topic)) score += 5;
    
    // Headings (20 points)
    const h2Count = (content.content.match(/^##\s/gm) || []).length;
    const h3Count = (content.content.match(/^###\s/gm) || []).length;
    if (h2Count >= 3) score += 10;
    if (h3Count >= 2) score += 10;
    
    // Content length (15 points)
    if (wordCount >= context.wordCount * 0.9) score += 15;
    
    // Internal structure (15 points)
    if (content.content.includes('##')) score += 5;
    if (content.content.includes('-') || content.content.includes('1.')) score += 5;
    if (content.content.includes('**') || content.content.includes('*')) score += 5;
    
    return Math.min(score, 100);
  }

  /**
   * Calculate readability score
   */
  private calculateReadabilityScore(content: string): number {
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = this.countWords(content);
    const avgWordsPerSentence = words / sentences.length;
    
    // Simple readability calculation
    let score = 100;
    
    // Penalize very long sentences
    if (avgWordsPerSentence > 20) score -= 20;
    else if (avgWordsPerSentence > 15) score -= 10;
    
    // Check for paragraph length
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);
    const avgWordsPerParagraph = words / paragraphs.length;
    if (avgWordsPerParagraph > 150) score -= 10;
    
    // Check for formatting
    if (!content.includes('##')) score -= 10;
    if (!content.includes('-') && !content.includes('1.')) score -= 10;
    
    return Math.max(score, 60);
  }

  /**
   * Identify uniqueness factors
   */
  private identifyUniquenessFactors(
    content: WritingOutput,
    analysis: WritingAnalysis
  ): string[] {
    const factors = [];
    
    // Check for comprehensive coverage
    if (this.countWords(content.content) > 2000) {
      factors.push('Comprehensive in-depth coverage');
    }
    
    // Check for data integration
    if ((content.content.match(/\d+%/g) || []).length > 5) {
      factors.push('Data-driven with multiple statistics');
    }
    
    // Check for structured format
    if (content.content.includes('##') && content.content.includes('###')) {
      factors.push('Well-structured hierarchical organization');
    }
    
    // Check for practical elements
    if (content.content.includes('Step') || content.content.includes('Tip')) {
      factors.push('Actionable practical guidance');
    }
    
    // Check for comparison elements
    if (content.content.includes('|') || content.content.includes('vs')) {
      factors.push('Comparative analysis included');
    }
    
    // Check for engagement elements
    if (content.content.includes('?')) {
      factors.push('Interactive questioning approach');
    }
    
    // Add niche-specific factors
    factors.push(`Optimized for ${analysis.nicheRequirements.essentialElements[0]}`);
    
    // Add article type factors
    factors.push(`Perfect ${analysis.articleTypeStructure.requiredSections[0]} structure`);
    
    return factors;
  }
}

// Export for use in invincible agent
export default EnhancedWritingSystem; 