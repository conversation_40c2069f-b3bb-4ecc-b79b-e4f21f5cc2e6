'use client'

import React, { useState, useEffect, Suspense, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import dynamic from 'next/dynamic'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { 
  Sparkles,
  Zap,
  Globe,
  Shield,
  Rocket,
  ArrowRight,
  Plus,
  Filter,
  Search,
  Bell,
  Grid,
  List,
  ChevronRight,
  TrendingUp,
  Clock,
  FileText,
  Award,
  Layers,
  Activity,
  Command,
  Settings,
  LogOut,
  User,
  HelpCircle,
  Home,
  PenTool,
  Mail,
  Share2,
  Youtube,
  Menu,
  X,
  ChevronLeft,
  Palette,
  Wand2,
  Lightbulb,
  Target,
  Compass,
  Cpu,
  BarChart,
  MessageSquare,
  Crown,
  Edit3,
  Video,
  Eye,
  Bot
} from 'lucide-react'
import { cn } from '@/lib/utils'
import ProfileButton from '@/components/ProfileButton'

// Lazy load RecentContent to reduce initial bundle size
const OptimizedRecentContent = dynamic(() => import('@/components/dashboard/RecentContent').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <div key={i} className="bg-white/10 rounded-lg p-4 animate-pulse">
          <div className="h-4 bg-white/10 rounded mb-2 w-3/4"></div>
          <div className="h-3 bg-white/10 rounded w-1/2"></div>
        </div>
      ))}
    </div>
  )
})

// Optimized lazy loading with React.memo and reduced bundle size
const OptimizedInvincibleOrb = dynamic(() => import('@/components/InvincibleOrb').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-violet-500/30 border-t-violet-500 rounded-full animate-spin mx-auto" />
})

const OptimizedBlogPreview = dynamic(() => import('@/components/BlogPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-pink-500/30 border-t-pink-500 rounded-full animate-spin mx-auto" />
})

const OptimizedEmailPreview = dynamic(() => import('@/components/EmailPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin mx-auto" />
})

const OptimizedSocialMediaPreview = dynamic(() => import('@/components/SocialMediaPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto" />
})

const OptimizedVideoScriptPreview = dynamic(() => import('@/components/VideoScriptPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto" />
})

const OptimizedVideoAlchemyPreview = dynamic(() => import('@/components/VideoAlchemyPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto" />
})

const OptimizedMegatronPreview = dynamic(() => import('@/components/MegatronPreview').then(mod => ({ default: React.memo(mod.default) })), {
  ssr: false,
  loading: () => <div className="w-16 h-16 border-2 border-red-500/30 border-t-red-500 rounded-full animate-spin mx-auto" />
})



// Optimized Sidebar Component with Memoization
const EnhancedSidebar = React.memo(({ 
  isOpen, 
  onClose, 
  tools, 
  selectedTool, 
  setSelectedTool, 
  hoveredTool, 
  setHoveredTool 
}: {
  isOpen: boolean
  onClose: () => void
  tools: Tool[]
  selectedTool: string
  setSelectedTool: (tool: string) => void
  hoveredTool: string | null
  setHoveredTool: (tool: string | null) => void
}) => {
  // Memoized animation variants for better performance
  const sidebarVariants = useMemo(() => ({
    open: {
      x: 0,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    },
    closed: {
      x: -320,
      transition: { type: "spring", stiffness: 300, damping: 30 }
    }
  }), [])

  // Memoized handlers to prevent re-renders
  const handleToolSelect = useCallback((toolId: string) => {
    setSelectedTool(toolId)
  }, [setSelectedTool])

  const handleToolHover = useCallback((toolId: string | null) => {
    setHoveredTool(toolId)
  }, [setHoveredTool])

  return (
    <>
      {/* Optimized Mobile Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* Optimized Sidebar */}
      <motion.aside
        initial="closed"
        animate={isOpen ? "open" : "closed"}
        variants={sidebarVariants}
        className="fixed left-0 top-0 h-screen w-[320px] z-50 lg:z-10"
        style={{ willChange: 'transform' }}
      >
        {/* Simplified Background */}
        <div className="absolute inset-0 bg-black/85 backdrop-blur-xl border-r border-white/10">
          <div className="absolute inset-0 bg-gradient-to-br from-violet-950/15 via-black/60 to-indigo-950/15" />
        </div>

        {/* Sidebar Content */}
        <div className="relative z-10 h-full flex flex-col">
          {/* Simplified Logo Section */}
          <div className="p-6 border-b border-white/10">
            <Link href="/" className="flex items-center space-x-4 group">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-violet-500 to-indigo-500 rounded-xl blur-lg opacity-50" />
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="relative bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl p-3 border border-white/20"
                >
                  <Sparkles className="w-7 h-7 text-white" />
                </motion.div>
              </div>
              
              <div className="space-y-1">
                <h1 className="text-2xl font-bold text-white">Invincible</h1>
                <p className="text-sm text-gray-400">Creative AI Suite</p>
              </div>
            </Link>
          </div>

          {/* Optimized Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto custom-scrollbar">
            {/* Dashboard Home */}
            <motion.button
              onClick={() => handleToolSelect('')}
              whileHover={{ x: 2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.1 }}
              className={cn(
                "w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative",
                !selectedTool
                  ? "bg-white/15 text-white shadow-lg border border-white/20"
                  : "text-gray-400 hover:text-white hover:bg-white/8"
              )}
            >
              <div className="flex items-center space-x-4">
                <div className="p-2 rounded-lg bg-white/10">
                  <Home className="w-5 h-5" />
                </div>
                <div className="text-left">
                  <p className="font-semibold">Dashboard</p>
                  <p className="text-xs opacity-70">Overview & Stats</p>
                </div>
              </div>
              
              {!selectedTool && (
                <div className="absolute right-3 w-1 h-8 bg-white rounded-full" />
              )}
            </motion.button>

            {/* Section Divider */}
            <div className="pt-6 pb-3">
              <div className="flex items-center space-x-3 px-4">
                <div className="h-px flex-1 bg-white/20" />
                <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                  AI Tools
                </p>
                <div className="h-px flex-1 bg-white/20" />
              </div>
            </div>

            {/* Content Library */}
            <Link href="/content">
              <motion.button
                whileHover={{ x: 2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                className="w-full flex items-center space-x-4 px-4 py-4 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200"
              >
                <div className="p-2 rounded-lg bg-white/5">
                  <Layers className="w-5 h-5" />
                </div>
                <div className="text-left">
                  <p className="font-semibold">Content Library</p>
                  <p className="text-xs opacity-70">View Past Content</p>
                </div>
              </motion.button>
            </Link>

            {/* Optimized Tool Navigation */}
            {tools.map((tool) => (
              <motion.button
                key={tool.id}
                onClick={() => handleToolSelect(tool.id)}
                onMouseEnter={() => handleToolHover(tool.id)}
                onMouseLeave={() => handleToolHover(null)}
                whileHover={{ x: 2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.1 }}
                className={cn(
                  "w-full flex items-center space-x-4 px-4 py-4 rounded-xl transition-all duration-200 relative",
                  selectedTool === tool.id
                    ? "bg-gradient-to-r text-white shadow-lg border border-white/20"
                    : "text-gray-400 hover:text-white hover:bg-white/8",
                  selectedTool === tool.id && tool.color
                )}
              >
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    "p-2 rounded-lg transition-colors duration-200",
                    selectedTool === tool.id
                      ? "bg-white/20"
                      : "bg-white/10"
                  )}>
                    <tool.icon className="w-5 h-5" />
                  </div>
                  <div className="text-left">
                    <div className="flex items-center gap-2">
                      <p className="font-semibold">{tool.title}</p>
                      {tool.beta && (
                        <span className="px-1.5 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded border border-blue-500/30">
                          BETA
                        </span>
                      )}
                      {tool.comingSoon && (
                        <span className="px-1.5 py-0.5 text-xs bg-gray-500/20 text-gray-400 rounded border border-gray-500/30">
                          SOON
                        </span>
                      )}
                    </div>
                    <p className="text-xs opacity-70">{tool.subtitle}</p>
                  </div>
                </div>

                {selectedTool === tool.id && (
                  <div className="absolute right-3 w-1 h-8 bg-white rounded-full" />
                )}

                {/* Simplified stats display */}
                {hoveredTool === tool.id && (
                  <div className="absolute right-3 top-3 bg-black/60 rounded-full px-2 py-1 text-xs text-white">
                    {tool.stats.generated}
                  </div>
                )}
              </motion.button>
            ))}
          </nav>

          {/* Simplified Bottom Actions */}
          <div className="p-4 border-t border-white/10 space-y-2">
            <Link href="/settings">
              <motion.button
                whileHover={{ x: 2 }}
                whileTap={{ scale: 0.98 }}
                transition={{ duration: 0.2 }}
                className="w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200"
              >
                <div className="p-2 rounded-lg bg-white/5">
                  <Settings className="w-5 h-5" />
                </div>
                <span className="font-medium">Settings</span>
              </motion.button>
            </Link>
            
            <motion.button
              whileHover={{ x: 2 }}
              whileTap={{ scale: 0.98 }}
              transition={{ duration: 0.2 }}
              className="w-full flex items-center space-x-4 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/8 rounded-xl transition-all duration-200"
            >
              <div className="p-2 rounded-lg bg-white/5">
                <HelpCircle className="w-5 h-5" />
              </div>
              <span className="font-medium">Help & Support</span>
            </motion.button>
          </div>
        </div>
      </motion.aside>
    </>
  )
})

// User profile interface
interface UserProfile {
  id: string
  name: string | null
  email: string | null
  image: string | null
  firstName: string | null
  lastName: string | null
  bio: string | null
  subscription?: {
    plan: string
    status: string
  }
  stats?: {
    totalContent: number
    totalUsage: number
  }
}

// Tool interface
interface Tool {
  id: string
  title: string
  subtitle: string
  description: string
  icon: any
  color: string
  bgGradient: string
  accentColor: string
  stats: {
    generated: number
    quality: number
    avgTime: string
  }
  features: string[]
  href: string
  preview?: React.ReactNode
  comingSoon?: boolean
  beta?: boolean
}

// Optimized Dashboard with Performance Enhancements
export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [selectedTool, setSelectedTool] = useState<string>('invincible-agent')
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [showNotifications, setShowNotifications] = useState(false)
  const [hoveredTool, setHoveredTool] = useState<string | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoadingProfile, setIsLoadingProfile] = useState(true)
  const [userStats, setUserStats] = useState<any>(null)
  const [isLoadingStats, setIsLoadingStats] = useState(true)

  // ALL useCallback hooks must be at the top level and in consistent order
  const toggleSidebar = useCallback(() => setSidebarOpen(prev => !prev), [])
  const toggleNotifications = useCallback(() => setShowNotifications(prev => !prev), [])
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }, [])

  // Memoized stats calculation for tools to prevent recalculation on every render
  const getToolStats = useCallback((toolType: string) => {
    if (!userStats?.contentBreakdown) {
      return { generated: 0, quality: 9.0, avgTime: '0 min' }
    }

    const generated = userStats.contentBreakdown[toolType] || 0
    const quality = Math.min(9.8, 8.5 + (generated * 0.05))
    
    const avgTimes: Record<string, string> = {
      'invincible_research': '4 min',
      'blog': '3 min',
      'email': '1 min',
      'social_media': '30 sec',
      'youtube_script': '4 min',
      'video_alchemy': '3 min'
    }

    return {
      generated,
      quality: Math.round(quality * 10) / 10,
      avgTime: avgTimes[toolType] || '2 min'
    }
  }, [userStats?.contentBreakdown])

  // Memoized tools configuration to prevent recreation - MUST be before any conditional returns
  const tools: Tool[] = useMemo(() => [
    {
      id: 'invincible-agent',
      title: 'Invincible V.1',
      subtitle: 'Superior Content',
      description: 'RAG-based content generation that analyzes competition, understands human writing patterns, and creates superior articles that dominate search results.',
      icon: Crown,
      color: 'from-violet-800/80 to-indigo-800/80',
      bgGradient: 'from-violet-950/30 to-indigo-950/30',
      accentColor: 'violet',
      stats: getToolStats('invincible_research'),
      features: ['RAG Research', 'Competitive Analysis', 'Human Writing Style', 'SEO Optimization', 'Superior Quality', 'Knowledge Base'],
      href: '/invincible',
      preview: <OptimizedInvincibleOrb />
    },
    {
      id: 'email-generator',
      title: 'Email Generator',
      subtitle: 'Professional Emails',
      description: 'Generate compelling email campaigns, newsletters, and professional communications with AI-powered personalization.',
      icon: Mail,
      color: 'from-emerald-500 to-teal-600',
      bgGradient: 'from-emerald-950/30 to-teal-950/30',
      accentColor: 'emerald',
      stats: getToolStats('email'),
      features: ['Personalization', 'A/B Testing', 'Professional Tone', 'Quick Generation'],
      href: '/email-generator'
    },
    {
      id: 'social-media-generator',
      title: 'Social Media',
      subtitle: 'Viral Content',
      description: 'Create engaging social media posts, captions, and content strategies that resonate with your audience across all platforms.',
      icon: Zap,
      color: 'from-pink-500 to-rose-600',
      bgGradient: 'from-pink-950/30 to-rose-950/30',
      accentColor: 'pink',
      stats: getToolStats('social_media'),
      features: ['Multi-Platform', 'Trending Hashtags', 'Engagement Optimization', 'Quick Generation'],
      href: '/social-media-generator'
    },
    {
      id: 'blog-generator',
      title: 'Blog Generator',
      subtitle: 'SEO Optimized',
      description: 'Generate comprehensive, SEO-optimized blog posts with research, proper structure, and engaging content that ranks well.',
      icon: FileText,
      color: 'from-blue-500 to-indigo-600',
      bgGradient: 'from-blue-950/30 to-indigo-950/30',
      accentColor: 'blue',
      stats: getToolStats('blog'),
      features: ['SEO Optimization', 'Research Integration', 'Long-form Content', 'Professional Structure'],
      href: '/blog-generator'
    },
    {
      id: 'youtube-script',
      title: 'YouTube Scripts',
      subtitle: 'Video Content',
      description: 'Create compelling YouTube video scripts with hooks, engagement techniques, and structured content for maximum viewer retention.',
      icon: Video,
      color: 'from-red-500 to-orange-600',
      bgGradient: 'from-red-950/30 to-orange-950/30',
      accentColor: 'red',
      stats: getToolStats('youtube_script'),
      features: ['Hook Generation', 'Retention Optimization', 'CTA Integration', 'Script Structure'],
      href: '/youtube-script'
    },
    {
      id: 'video-alchemy',
      title: 'Video Alchemy',
      subtitle: 'Coming Soon',
      description: 'Transform your ideas into stunning video content with AI-powered video generation and editing capabilities.',
      icon: Sparkles,
      color: 'from-purple-500 to-violet-600',
      bgGradient: 'from-purple-950/30 to-violet-950/30',
      accentColor: 'purple',
      stats: getToolStats('video_alchemy'),
      features: ['AI Video Generation', 'Auto Editing', 'Style Transfer', 'Quick Export'],
      href: '#',
      comingSoon: true
    },
    {
      id: 'megatron',
      title: 'Megatron',
      subtitle: 'Ultimate AI',
      description: 'The most powerful AI assistant for complex tasks, research, analysis, and creative projects with unlimited potential.',
      icon: Bot,
      color: 'from-gray-600 to-slate-700',
      bgGradient: 'from-gray-950/30 to-slate-950/30',
      accentColor: 'gray',
      stats: getToolStats('megatron'),
      features: ['Advanced Reasoning', 'Multi-task Handling', 'Research Capabilities', 'Creative Solutions'],
      href: '#',
      comingSoon: true
    },

  ], [getToolStats])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login')
    }
  }, [status, router])

  // Fetch user profile data
  useEffect(() => {
    if (session?.user) {
      fetchUserProfile()
      fetchUserStats()
    }
  }, [session])

  const fetchUserProfile = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (response.ok) {
        const data = await response.json()
        setUserProfile(data)
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    } finally {
      setIsLoadingProfile(false)
    }
  }

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/stats')
      if (response.ok) {
        const data = await response.json()
        setUserStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching user stats:', error)
    } finally {
      setIsLoadingStats(false)
    }
  }

  // Generate user initials for avatar
  const getUserInitials = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName[0]}${userProfile.lastName[0]}`
    } else if (userProfile?.name) {
      const names = userProfile.name.split(' ')
      return names.length > 1 ? `${names[0][0]}${names[names.length - 1][0]}` : names[0][0]
    } else if (userProfile?.email) {
      return userProfile.email[0].toUpperCase()
    }
    return 'U'
  }

  // Get display name
  const getDisplayName = () => {
    if (userProfile?.firstName && userProfile?.lastName) {
      return `${userProfile.firstName} ${userProfile.lastName}`
    } else if (userProfile?.name) {
      return userProfile.name
    } else if (userProfile?.email) {
      return userProfile.email.split('@')[0]
    }
    return 'User'
  }

  // Loading state
  if (status === 'loading' || isLoadingProfile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"></div>
          <p className="text-gray-400">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const activeTool = tools.find(t => t.id === selectedTool) || tools[0]

  return (
    <div className="min-h-screen bg-black">
      {/* Animated Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10" />
        
        {/* Dynamic gradient based on selected tool */}
        <motion.div
          key={activeTool.id}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, ease: [0.4, 0, 0.2, 1] }}
          className={cn(
            "absolute inset-0 bg-gradient-to-br opacity-20",
            activeTool.bgGradient
          )}
        />
        
        {/* Optimized static orbs - removed heavy animations */}
        <div className="absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px] animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px] animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      {/* Main Layout */}
      <div className="relative z-10 flex min-h-screen">
        {/* Enhanced Sidebar */}
        <EnhancedSidebar
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          tools={tools}
          selectedTool={selectedTool}
          setSelectedTool={setSelectedTool}
          hoveredTool={hoveredTool}
          setHoveredTool={setHoveredTool}
        />

        {/* Optimized Sidebar Toggle */}
        <motion.button
          onClick={toggleSidebar}
          animate={{ 
            left: sidebarOpen ? 308 : 20
          }}
          transition={{ 
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="fixed top-6 z-50 p-3 bg-black/80 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-black/90 transition-colors duration-200"
          style={{ willChange: 'transform' }}
        >
          {sidebarOpen ? <ChevronLeft className="w-5 h-5 text-white" /> : <Menu className="w-5 h-5 text-white" />}
        </motion.button>

        {/* Main Content Area */}
        <motion.main
          animate={{ 
            marginLeft: sidebarOpen ? 320 : 0
          }}
          transition={{ 
            type: "tween",
            duration: 0.3,
            ease: [0.4, 0, 0.2, 1]
          }}
          className="flex-1 min-h-screen"
          style={{ willChange: 'margin-left' }}
        >
          {/* Top Bar */}
                      <header className="sticky top-0 z-40 backdrop-blur-xl bg-black/60 border-b border-white/10">
              <motion.div
                animate={{ paddingLeft: sidebarOpen ? 32 : 80 }}
                transition={{ 
                  type: "tween",
                  duration: 0.3,
                  ease: [0.4, 0, 0.2, 1]
                }}
                className="px-8 py-6"
              >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-8">
                                      <h2 className="text-3xl font-bold text-white">
                      {selectedTool ? activeTool.title : 'Dashboard Overview'}
                    </h2>
                    
                    {/* Optimized Search */}
                    <div className="relative">
                      <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search tools, features..."
                        value={searchQuery}
                        onChange={handleSearchChange}
                        className="w-96 pl-12 pr-4 py-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/15 focus:border-violet-500/50 focus:outline-none transition-colors duration-200"
                      />
                    </div>
                </div>

                                  {/* Optimized Actions */}
                  <div className="flex items-center space-x-4">
                    <button
                      className="relative p-3 text-gray-400 hover:text-white transition-colors duration-200 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl hover:bg-white/15"
                      onClick={toggleNotifications}
                    >
                      <Bell className="w-5 h-5" />
                      <span className="absolute top-2 right-2 w-2 h-2 bg-violet-500 rounded-full" />
                    </button>

                    <div className="h-8 w-px bg-white/20" />

                    <ProfileButton 
                      userProfile={userProfile} 
                      className="pl-0"
                    />
                  </div>
              </div>
            </motion.div>
          </header>

                      {/* Optimized Content Area */}
            <motion.div
              animate={{ 
                paddingLeft: sidebarOpen ? 32 : 80
              }}
              transition={{ 
                type: "tween",
                duration: 0.3,
                ease: [0.4, 0, 0.2, 1]
              }}
              className="p-8 pb-16 pr-8"
            >
            <AnimatePresence mode="wait">
              {selectedTool ? (
                <ToolDetails key={selectedTool} tool={activeTool} />
              ) : (
                <DashboardOverview key="overview" tools={tools} userProfile={userProfile} getDisplayName={getDisplayName} userStats={userStats} isLoadingStats={isLoadingStats} />
              )}
            </AnimatePresence>
          </motion.div>
        </motion.main>
      </div>


    </div>
  )
}

// Optimized Tool Details Component with Memoization
const ToolDetails = React.memo(({ tool }: { tool: Tool }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className="space-y-8"
    >
      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        className={cn(
          "relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8",
          tool.id === 'invincible-agent'
            ? "bg-gradient-to-br from-white/10 to-white/5 border-white/20 shadow-2xl"
            : "bg-gradient-to-br from-white/5 to-white/0 border-white/10"
        )}>
        {/* Glass reflection overlay for Invincible */}
        {tool.id === 'invincible-agent' && (
          <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl" />
        )}
        
        <div className={cn(
          "absolute inset-0 bg-gradient-to-br opacity-20",
          tool.color
        )} />
        
        <div className="relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div className={cn(
                "p-4 rounded-2xl text-white shadow-2xl",
                tool.id === 'invincible-agent'
                  ? "bg-gradient-to-br from-violet-800/70 to-indigo-800/70 backdrop-blur-sm border border-white/30"
                  : cn("bg-gradient-to-br", tool.color)
              )}>
                <tool.icon className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white">{tool.title}</h1>
                <p className="text-lg text-gray-300">{tool.subtitle}</p>
              </div>
            </div>

            <p className="text-gray-300 leading-relaxed">
              {tool.description}
            </p>

            <div className="flex flex-wrap gap-3">
              {tool.features.map((feature, index) => (
                <span
                  key={index}
                  className={cn(
                    "px-4 py-2 rounded-full text-sm text-white border",
                    tool.id === 'invincible-agent'
                      ? "bg-white/15 backdrop-blur-sm border-white/30"
                      : "bg-white/10 backdrop-blur-sm border-white/20"
                  )}
                >
                  {feature}
                </span>
              ))}
            </div>

            <div className="flex items-center space-x-4">
              <Link href={tool.href}>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={cn(
                    "px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3",
                    tool.id === 'invincible-agent'
                      ? "bg-gradient-to-r from-violet-800/80 to-indigo-800/80 backdrop-blur-sm border border-white/30 hover:from-violet-700/90 hover:to-indigo-700/90"
                      : cn("bg-gradient-to-r", tool.color)
                  )}
                >
                  <Rocket className="w-6 h-6" />
                  <span>Launch {tool.title}</span>
                </motion.button>
              </Link>
            </div>
          </div>

          {/* Preview Area */}
          <div className={cn(
            "relative h-[400px] rounded-2xl overflow-hidden border",
            tool.id === 'invincible-agent'
              ? "bg-black/30 backdrop-blur-sm border-white/20"
              : "bg-black/40 border-white/10"
          )}>
            {tool.preview || (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-4">
                  <tool.icon className="w-24 h-24 text-white/20 mx-auto" />
                  <p className="text-gray-400">Interactive preview coming soon</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </motion.div>

      {/* Stats Cards */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
      >
        <Link href={`/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.25 }}
            className={cn(
              "backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group",
              tool.id === 'invincible-agent'
                ? "bg-white/10 border-white/20 shadow-xl hover:border-white/30"
                : "bg-white/5 border-white/10 hover:border-white/20"
            )}
            title={`View all ${tool.title} content in your library`}
          >
            <div className="flex items-center justify-between mb-4">
              <FileText className={cn("w-8 h-8 group-hover:scale-110 transition-transform duration-200", `text-${tool.accentColor}-400`)} />
              <div className="flex items-center space-x-1">
                <TrendingUp className="w-5 h-5 text-emerald-400" />
                <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" />
              </div>
            </div>
            <p className="text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors">{tool.stats.generated}</p>
            <p className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors">Content Generated - Click to view</p>
            
            {/* Subtle hover indicator */}
            <div className="mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="flex items-center text-xs text-violet-300">
                <Eye className="w-3 h-3 mr-1" />
                <span>View in Content Library</span>
              </div>
            </div>
          </motion.div>
        </Link>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.35 }}
          className={cn(
            "backdrop-blur-xl border rounded-2xl p-6",
            tool.id === 'invincible-agent'
              ? "bg-white/10 border-white/20 shadow-xl"
              : "bg-white/5 border-white/10"
          )}
        >
          <div className="flex items-center justify-between mb-4">
            <Award className={cn("w-8 h-8", `text-${tool.accentColor}-400`)} />
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <div
                  key={star}
                  className={cn(
                    "w-3 h-3 rounded-full",
                    star <= Math.round(tool.stats.quality / 2)
                      ? "bg-yellow-400"
                      : "bg-gray-600"
                  )}
                />
              ))}
            </div>
          </div>
          <p className="text-3xl font-bold text-white mb-1">{tool.stats.quality}/10</p>
          <p className="text-sm text-gray-400">Quality Score</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.45 }}
          className={cn(
            "backdrop-blur-xl border rounded-2xl p-6",
            tool.id === 'invincible-agent'
              ? "bg-white/10 border-white/20 shadow-xl"
              : "bg-white/5 border-white/10"
          )}
        >
          <div className="flex items-center justify-between mb-4">
            <Clock className={cn("w-8 h-8", `text-${tool.accentColor}-400`)} />
            <Activity className="w-5 h-5 text-blue-400" />
          </div>
          <p className="text-3xl font-bold text-white mb-1">{tool.stats.avgTime}</p>
          <p className="text-sm text-gray-400">Average Time</p>
        </motion.div>
      </motion.div>
    </motion.div>
  )
})

// Optimized Dashboard Overview Component with Memoization
const DashboardOverview = React.memo(({ tools, userProfile, getDisplayName, userStats, isLoadingStats }: { 
  tools: Tool[]
  userProfile: UserProfile | null
  getDisplayName: () => string
  userStats: any
  isLoadingStats: boolean
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      className="space-y-8"
    >
      {/* Welcome Section */}
              <div className="bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8">
        <h1 className="text-4xl font-bold text-white mb-4">Welcome back, {getDisplayName()}! ✨</h1>
        <p className="text-xl text-gray-300 mb-6">Your creative AI toolkit is ready. What will you create today?</p>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {isLoadingStats ? (
            // Loading skeleton
            Array.from({ length: 4 }).map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse"
              >
                <div className="h-5 bg-white/10 rounded mb-2"></div>
                <div className="h-8 bg-white/10 rounded mb-1"></div>
                <div className="h-4 bg-white/10 rounded"></div>
              </motion.div>
            ))
          ) : (
            [
              { 
                label: 'Total Created', 
                value: userStats?.totalContent?.toString() || '0', 
                icon: FileText, 
                change: userStats?.trends?.contentGrowth || '+0%' 
              },
              { 
                label: 'Time Saved', 
                value: `${userStats?.timeSavedHours || 0} hrs`, 
                icon: Clock, 
                change: userStats?.trends?.timeEfficiency || '+0%' 
              },
              { 
                label: 'Quality Score', 
                value: `${userStats?.qualityScore || 9.0}/10`, 
                icon: Award, 
                change: userStats?.trends?.qualityImprovement || '+0.0' 
              },
              { 
                label: 'Active Tools', 
                value: userStats?.trends?.toolsActive?.toString() || '0', 
                icon: Layers, 
                change: '+' + (userStats?.trends?.toolsActive || 0)
              }
            ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4"
            >
              <div className="flex items-center justify-between mb-2">
                <stat.icon className="w-5 h-5 text-violet-400" />
                <span className="text-xs text-emerald-400">{stat.change}</span>
              </div>
              <p className="text-2xl font-bold text-white">{stat.value}</p>
              <p className="text-sm text-gray-400">{stat.label}</p>
            </motion.div>
          ))
          )}
        </div>
      </div>

      {/* Tools Grid */}
      <div>
        <h2 className="text-2xl font-bold text-white mb-6">Your AI Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tools.map((tool, index) => (
            <Link key={tool.id} href={tool.href}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -8 }}
                className="group relative cursor-pointer"
              >
                <div className={cn(
                  "absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl",
                  tool.color
                )} />
                
                <div className={cn(
                  "relative backdrop-blur-xl border transition-all",
                  tool.id === 'invincible-agent' 
                    ? "bg-black/40 border-white/20 hover:border-white/30 rounded-2xl shadow-2xl"
                    : "bg-black/60 border-white/10 hover:border-white/20 rounded-2xl"
                )}>
                  {/* Glass reflection effect for Invincible */}
                  {tool.id === 'invincible-agent' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl" />
                  )}
                  
                  <div className="relative p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className={cn(
                        "p-3 rounded-xl text-white",
                        tool.id === 'invincible-agent'
                          ? "bg-gradient-to-br from-violet-800/60 to-indigo-800/60 backdrop-blur-sm border border-white/20"
                          : cn("bg-gradient-to-br", tool.color)
                      )}>
                        <tool.icon className="w-6 h-6" />
                      </div>
                      <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-white transition-colors" />
                    </div>

                    <h3 className="text-lg font-semibold text-white mb-1">{tool.title}</h3>
                    <p className="text-sm text-gray-400 mb-4">{tool.subtitle}</p>

                    <div className="flex items-center justify-between text-xs">
                      <Link 
                        href={`/content?type=${tool.id === 'invincible-agent' ? 'invincible_research' : tool.id === 'blog-generator' ? 'blog' : tool.id === 'email-generator' ? 'email' : tool.id === 'youtube-script' ? 'youtube_script' : tool.id.replace('-', '_')}`}
                        onClick={(e) => e.stopPropagation()}
                        className="hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat"
                        title={`View all ${tool.title} content`}
                      >
                        <span className="text-gray-500 group-hover/stat:text-gray-400">Generated</span>
                        <div className="flex items-center space-x-1">
                          <p className="text-white font-medium group-hover/stat:text-violet-200">{tool.stats.generated}</p>
                          <ArrowRight className="w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all" />
                        </div>
                        {/* Tooltip on hover */}
                        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                          Click to view content
                        </div>
                      </Link>
                      <div className="text-right">
                        <span className="text-gray-500">Quality</span>
                        <p className="text-white font-medium">{tool.stats.quality}/10</p>
                                  </div>
          </div>
        </div>
      </div>
              </motion.div>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Content */}
      <div className="bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6">
        <OptimizedRecentContent limit={5} showFilters={true} />
      </div>
    </motion.div>
  )
})
