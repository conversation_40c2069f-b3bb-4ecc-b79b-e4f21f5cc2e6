/**
 * KaibanJS Ultron Agent System API Route
 * Handles YouTube script generation using multi-agent system
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateScript, generateMultipleScripts, getTeamInfo } from '@/lib/agents/ultron-kaiban/ultron-team';
import type { UltronTeamConfig } from '@/lib/agents/ultron-kaiban/ultron-team';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      action = 'generate', 
      topic,
      targetAudience,
      scriptStyle,
      videoLength,
      includeHooks,
      includeCallToAction,
      teamType = 'standard',
      topics // for batch generation
    } = body;

    // Validate required parameters
    if (action === 'generate' && !topic) {
      return NextResponse.json(
        { error: 'Topic is required for script generation' },
        { status: 400 }
      );
    }

    if (action === 'batch' && (!topics || !Array.isArray(topics))) {
      return NextResponse.json(
        { error: 'Topics array is required for batch generation' },
        { status: 400 }
      );
    }

    // Handle different actions
    switch (action) {
      case 'generate': {
        const config: UltronTeamConfig = {
          topic,
          targetAudience,
          scriptStyle,
          videoLength,
          includeHooks,
          includeCallToAction,
          teamType
        };

        console.log('🤖 Starting Ultron script generation:', config);

        const result = await generateScript(config);

        return NextResponse.json({
          success: result.success,
          script: result.script,
          error: result.error,
          metadata: result.metadata,
          teamInfo: getTeamInfo(teamType)
        });
      }

      case 'batch': {
        const baseConfig = {
          targetAudience,
          scriptStyle,
          videoLength,
          includeHooks,
          includeCallToAction,
          teamType
        };

        console.log('🚀 Starting batch script generation for', topics.length, 'topics');

        const results = await generateMultipleScripts(topics, baseConfig);

        return NextResponse.json({
          success: true,
          results,
          summary: {
            total: results.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length
          }
        });
      }

      case 'info': {
        const teamInfo = getTeamInfo(teamType);
        return NextResponse.json({
          success: true,
          teamInfo,
          availableTeams: ['standard', 'advanced', 'quick'],
          availableStyles: ['educational', 'entertainment', 'documentary', 'tutorial', 'review']
        });
      }

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use "generate", "batch", or "info"' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('❌ Ultron API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const teamType = searchParams.get('team') as 'standard' | 'advanced' | 'quick' || 'standard';

    const teamInfo = getTeamInfo(teamType);

    return NextResponse.json({
      success: true,
      teamInfo,
      availableTeams: ['standard', 'advanced', 'quick'],
      availableStyles: ['educational', 'entertainment', 'documentary', 'tutorial', 'review'],
      usage: {
        endpoint: '/api/ultron/kaiban',
        methods: ['GET', 'POST'],
        actions: {
          generate: 'Generate a single script',
          batch: 'Generate multiple scripts',
          info: 'Get team information'
        },
        parameters: {
          topic: 'Required for generate action',
          targetAudience: 'Optional - defaults to "General YouTube viewers"',
          scriptStyle: 'Optional - defaults to "educational"',
          videoLength: 'Optional - defaults to 10 minutes',
          includeHooks: 'Optional - defaults to true',
          includeCallToAction: 'Optional - defaults to true',
          teamType: 'Optional - "standard", "advanced", or "quick"'
        }
      }
    });

  } catch (error) {
    console.error('❌ Ultron GET API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 