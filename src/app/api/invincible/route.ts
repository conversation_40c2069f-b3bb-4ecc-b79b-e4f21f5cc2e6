/**
 * API Route for Invincible V.1 Agent - OPTIMIZED
 * Handles requests to generate superior content using the RAG-based approach
 * Enhanced with session caching to prevent repeated auth queries
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { QuotaManager } from '@/lib/quota'
import { prisma } from '@/lib/prisma'
import { InvincibleAgent } from '@/lib/agents/invincible-agent';
import { TaskContext } from '@/lib/agents/types';
import { createProgressManager } from '@/lib/progress-manager';

export const maxDuration = 300; // 5 minutes for comprehensive analysis

// Daily session cache - one auth per day per user
const dailySessionCache = new Map<string, { session: any; expires: number }>();

// Track cleanup interval to avoid multiple intervals
let cleanupInterval: NodeJS.Timeout | null = null;

function getCachedDailySession(sessionToken?: string): any | null {
  if (!sessionToken) return null;
  
  const cached = dailySessionCache.get(sessionToken);
  if (cached && Date.now() < cached.expires) {
    return cached.session;
  }
  
  // Clean expired entries
  dailySessionCache.delete(sessionToken);
  return null;
}

function setCachedDailySession(sessionToken: string, session: any): void {
  // Cache for 24 hours (daily session)
  const oneDayInMs = 24 * 60 * 60 * 1000;
  dailySessionCache.set(sessionToken, {
    session,
    expires: Date.now() + oneDayInMs
  });
}

// Cleanup function to remove expired sessions periodically
function cleanupExpiredSessions(): void {
  const now = Date.now();
  for (const [token, cached] of dailySessionCache.entries()) {
    if (now >= cached.expires) {
      dailySessionCache.delete(token);
    }
  }
}

// Initialize cleanup interval only once
function initializeCleanupInterval(): void {
  if (!cleanupInterval) {
    cleanupInterval = setInterval(cleanupExpiredSessions, 60 * 60 * 1000); // Run cleanup every hour
    console.log('🧹 Session cleanup interval initialized');
  }
}

// Cleanup function for graceful shutdown
function cleanup(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
    console.log('🧹 Session cleanup interval cleared');
  }
  // Clear the session cache
  dailySessionCache.clear();
}

// Handle process termination gracefully
process.on('SIGTERM', cleanup);
process.on('SIGINT', cleanup);
process.on('exit', cleanup);

// Initialize on first load
initializeCleanupInterval();

export async function POST(req: NextRequest) {
  try {
    // Check authentication with daily caching
    const sessionToken = req.cookies.get('next-auth.session-token')?.value || 
                        req.cookies.get('__Secure-next-auth.session-token')?.value;
    
    let session = getCachedDailySession(sessionToken);
    
    if (!session) {
      console.log('🔐 Performing daily authentication check...');
      session = await getServerSession(authOptions);
      
      if (session && sessionToken) {
        setCachedDailySession(sessionToken, session); // Cache for 24 hours
        console.log('✅ Session cached for 24 hours - no more auth queries today');
      }
    } else {
      console.log('⚡ Using daily cached session - zero database queries');
    }

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Check quota (this still needs DB access but only once)
    console.log('📊 Checking quota...');
    const quotaCheck = await QuotaManager.checkQuota(session.user.id, 'invincible_research')
    if (!quotaCheck.hasQuota) {
      return NextResponse.json(
        { 
          error: 'Invincible research quota exceeded',
          quota: {
            used: quotaCheck.used,
            limit: quotaCheck.limit,
            resetDate: quotaCheck.resetDate,
            userEmail: session.user.email,
            plan: 'free'
          },
          message: `You've used ${quotaCheck.used}/${quotaCheck.limit} monthly Invincible research requests. ${quotaCheck.resetDate ? `Quota resets on ${new Date(quotaCheck.resetDate).toLocaleDateString()}.` : ''} Upgrade for unlimited access.`
        },
        { status: 429 }
      )
    }

    const body = await req.json();
    
    // Validate input
    if (!body.topic || typeof body.topic !== 'string') {
      return NextResponse.json(
        { error: 'Topic is required and must be a string' },
        { status: 400 }
      );
    }

    console.log('🚀 Starting Invincible workflow for authenticated user:', session.user.email);

    // Create task context
    const taskContext: TaskContext = {
      taskId: `invincible_${Date.now()}`,
      topic: body.topic,
      customInstructions: body.customInstructions,
      targetAudience: body.targetAudience,
      contentLength: body.contentLength || 2000,
      tone: body.tone,
      keywords: body.keywords,
      contentType: body.contentType || 'article'
    };

    // Create progress manager (disable if causing auth calls)
    const progressManager = createProgressManager(taskContext.taskId);
    
    // Initialize progress
    progressManager.updateProgress(5, 'Initializing Invincible V.1 Agent...');
    console.log('📈 Progress tracking initialized');

    // Create and configure agent with uniqueness settings
    const agent = new InvincibleAgent({
      searchDepth: body.searchDepth || 7,
      competitorCount: body.competitorCount || 5,
      deepSearchQueriesPerTopic: body.deepSearchQueriesPerTopic || 7,
      maxContentLength: body.maxContentLength || 15000,
      temperature: body.temperature || 0.7,
      uniquenessLevel: body.uniquenessLevel || 'high' // Default to high uniqueness
    });

    // Optimized progress updates - less frequent to reduce auth calls
    let lastProgress = 5;
    let progressInterval: NodeJS.Timeout | undefined;
    
    try {
      progressInterval = setInterval(() => {
        if (lastProgress < 85) {
          lastProgress += 15; // Larger increments, less frequent
          try {
            progressManager.updateProgress(lastProgress, 'Processing...');
          } catch (error) {
            console.log('⚠️ Progress update failed (non-critical):', error);
          }
        }
      }, 10000); // Every 10 seconds instead of 3

      console.log('🎯 Executing Invincible agent workflow...');
      
      // Execute the agent - this is where the main work happens
      const result = await agent.execute(taskContext);

      // Clear interval
      if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = undefined;
      }
      console.log('✅ Agent execution completed');

      if (!result.success) {
        progressManager.updateProgress(100, 'Failed', 'error');
        return NextResponse.json(
          { 
            error: result.error || 'Agent execution failed',
            logs: result.logs 
          },
          { status: 500 }
        );
      }

      // Complete progress
      progressManager.updateProgress(100, 'Article generated successfully!', 'completed');

      // Use quota - single DB call
      console.log('📊 Updating quota usage...');
      const quotaUsed = await QuotaManager.useQuota(session.user.id, 'invincible_research')
      if (!quotaUsed) {
        console.error('Failed to update quota after successful generation')
      }

      // Save content to database - single optimized insert
      try {
        console.log('💾 Saving generated content...');
        await prisma.content.create({
          data: {
            userId: session.user.id,
            type: 'invincible_research',
            title: result.article?.title || body.topic,
            content: result.article?.content || '',
            wordCount: result.article?.content?.length || 0,
            tone: body.tone || 'professional',
            metadata: JSON.stringify({
              taskId: taskContext.taskId,
              executionTime: result.executionTime,
              totalSources: result.researchData?.totalSources || 0,
              competitorsAnalyzed: result.competitiveAnalysis?.length || 0,
              keyInsights: result.researchData?.keyInsights?.length || 0,
              statistics: result.researchData?.statistics?.length || 0,
              researchQueries: result.researchData?.searchQueries || [],
              uniquenessScore: result.uniquenessScore || 0,
              contentFingerprint: result.contentFingerprint || '',
              uniquenessLevel: body.uniquenessLevel || 'high',
              generatedAt: new Date().toISOString()
            })
          }
        })
        console.log('✅ Content saved to database');
      } catch (dbError) {
        console.error('Failed to save content to database:', dbError)
        // Don't fail the request if saving fails
      }

      // Prepare response
      const response = {
        success: true,
        article: result.article,
        metadata: {
          taskId: taskContext.taskId,
          executionTime: result.executionTime,
          totalSources: result.researchData?.totalSources || 0,
          competitorsAnalyzed: result.competitiveAnalysis?.length || 0,
          keyInsights: result.researchData?.keyInsights?.length || 0,
          statistics: result.researchData?.statistics?.length || 0,
          uniquenessScore: result.uniquenessScore || 0,
          contentFingerprint: result.contentFingerprint || '',
          uniquenessLevel: body.uniquenessLevel || 'high'
        },
        research: {
          sources: result.researchData?.sources?.length || 0,
          queries: result.researchData?.searchQueries || [],
          keyInsights: result.researchData?.keyInsights?.slice(0, 10) || []
        },
        competitive: {
          analyzed: Array.isArray(result.competitiveAnalysis) ? result.competitiveAnalysis.length : 
                   (result.competitiveAnalysis ? 1 : 0),
          opportunities: Array.isArray(result.competitiveAnalysis) ? 
                        result.competitiveAnalysis.flatMap((ca: any) => ca.opportunities || []).slice(0, 10) :
                        ((result.competitiveAnalysis as any)?.criticalGaps || []).slice(0, 10),
          gaps: Array.isArray(result.competitiveAnalysis) ? 
               result.competitiveAnalysis.flatMap((ca: any) => ca.weaknesses || []).slice(0, 10) :
               ((result.competitiveAnalysis as any)?.depthDeficiencies || []).slice(0, 10)
        },
        logs: result.logs,
        quota: {
          used: quotaCheck.used + 1,
          limit: quotaCheck.limit,
          remaining: quotaCheck.limit === -1 ? -1 : quotaCheck.limit - (quotaCheck.used + 1)
        }
      };

      console.log('🎉 Workflow completed successfully');
      return NextResponse.json(response);

    } catch (error) {
      if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = undefined;
      }
      throw error;
    }

  } catch (error) {
    console.error('Invincible agent error:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}

// GET endpoint to check agent status
export async function GET(req: NextRequest) {
  return NextResponse.json({
    agent: 'Invincible V.1 - Daily Auth Optimized (Kimi K2)',
    version: '1.0.2',
    status: 'ready',
    optimizations: [
      '24-hour session caching - one auth per day',
      'Zero repeated database queries during workflows',
      'Reduced progress update frequency',
      'Optimized database operations',
      'Enhanced error handling and logging'
    ],
    capabilities: [
      'Primary search and URL extraction',
      'Article type analysis with Kimi K2',
      'Deep research with custom queries',
      'Competitive analysis',
      'Human writing style analysis',
      'Superior content generation with Kimi K2',
      'Knowledge base management'
    ],
    workflow: {
      steps: 6,
      description: 'RAG-based single agent system for superior content creation'
    }
  });
} 