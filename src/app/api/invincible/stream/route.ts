import { NextRequest, NextResponse } from 'next/server';
import { InvincibleAgent } from '@/lib/agents/invincible-agent';

// Create a streaming response for real-time Invincible Agent updates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const topic = searchParams.get('topic');
    const contentLength = searchParams.get('contentLength');
    const tone = searchParams.get('tone');
    const targetAudience = searchParams.get('targetAudience');
    const customInstructions = searchParams.get('customInstructions');

    // Validate required fields
    if (!topic) {
      return NextResponse.json({ error: 'Topic is required' }, { status: 400 });
    }

    // Create SSE response
    const encoder = new TextEncoder();
    const stream = new ReadableStream({
      start(controller) {
        // Function to send SSE message with error handling
        const sendEvent = (event: string, data: any) => {
          try {
            const message = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
            controller.enqueue(encoder.encode(message));
          } catch (error) {
            console.error(`Failed to send SSE event '${event}':`, error);
            throw error; // Re-throw to handle in calling code
          }
        };

        // Send periodic heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          try {
            sendEvent('heartbeat', { timestamp: Date.now() });
          } catch (error) {
            console.error('Heartbeat failed, clearing interval:', error);
            clearInterval(heartbeatInterval);
          }
        }, 30000); // Every 30 seconds

        // Start the generation process
        (async () => {
          try {
            sendEvent('start', {
              message: 'Initializing Invincible Agent...',
              timestamp: Date.now(),
              phase: 'initialization'
            });

            // Create enhanced agent with progress tracking
            const agent = new InvincibleAgent({
              searchDepth: 7,
              competitorCount: 10,
              deepSearchQueriesPerTopic: 15,
              maxContentLength: contentLength ? parseInt(contentLength) : 15000,
              temperature: 0.7,
              uniquenessLevel: 'high',
              enableNicheLearning: true
            });

            // Hook into agent's logging system
            const originalLog = agent['log'].bind(agent);
            agent['log'] = (message: string) => {
              originalLog(message);
              
              // Parse log message for different event types
              const timestamp = Date.now();
              
              if (message.includes('🔍 STEP 1:')) {
                sendEvent('phase', {
                  phase: 'primary_search',
                  message: 'Starting primary search and data extraction',
                  step: 1,
                  total: 4,
                  timestamp
                });
              } else if (message.includes('🧠 STEP 2:')) {
                sendEvent('phase', {
                  phase: 'content_analysis',
                  message: 'Analyzing content with Kimi K2 AI',
                  step: 2,
                  total: 4,
                  timestamp
                });
              } else if (message.includes('🔎 STEP 3:')) {
                sendEvent('phase', {
                  phase: 'comprehensive_research',
                  message: 'Performing comprehensive data scraping',
                  step: 3,
                  total: 4,
                  timestamp
                });
              } else if (message.includes('✍️ STEP 4:')) {
                sendEvent('phase', {
                  phase: 'content_generation',
                  message: 'Generating superior article with full analysis',
                  step: 4,
                  total: 4,
                  timestamp
                });
              } else if (message.includes('🔍 Starting search')) {
                const queryMatch = message.match(/Starting search \d+\/\d+: "([^"]+)"/);
                if (queryMatch) {
                  sendEvent('search_query', {
                    query: queryMatch[1],
                    message: `Searching: ${queryMatch[1]}`,
                    timestamp
                  });
                }
              } else if (message.includes('📊 Found') && message.includes('URLs')) {
                const urlMatch = message.match(/Found (\d+) URLs/);
                if (urlMatch) {
                  sendEvent('search_results', {
                    urlCount: parseInt(urlMatch[1]),
                    message: `Found ${urlMatch[1]} URLs for analysis`,
                    timestamp
                  });
                }
              } else if (message.includes('✅ Scraped:')) {
                const domainMatch = message.match(/Scraped: ([^)]+)/);
                if (domainMatch) {
                  sendEvent('scraping', {
                    domain: domainMatch[1],
                    message: `Analyzed content from ${domainMatch[1]}`,
                    timestamp
                  });
                }
              } else if (message.includes('📋 Detected') && message.includes('article type')) {
                const typeMatch = message.match(/article type detected/);
                if (typeMatch) {
                  sendEvent('analysis', {
                    type: 'article_type',
                    message: 'Article type and structure analyzed',
                    timestamp
                  });
                }
              } else if (message.includes('✅ Content analysis complete')) {
                sendEvent('analysis', {
                  type: 'content_complete',
                  message: 'Content analysis completed successfully',
                  timestamp
                });
              } else if (message.includes('✅ Competition analysis complete')) {
                sendEvent('analysis', {
                  type: 'competition_complete',
                  message: 'Competition analysis completed',
                  timestamp
                });
              } else if (message.includes('✅ Human writing analysis complete')) {
                sendEvent('analysis', {
                  type: 'human_writing_complete',
                  message: 'Human writing patterns analyzed',
                  timestamp
                });
              } else if (message.includes('🔄 Fixing') && message.includes('instances')) {
                const fixMatch = message.match(/Fixing (\d+) instances of "([^"]+)"/);
                if (fixMatch) {
                  sendEvent('humanization', {
                    type: 'date_fixing',
                    count: parseInt(fixMatch[1]),
                    target: fixMatch[2],
                    message: `Fixed ${fixMatch[1]} instances of repetitive dates`,
                    timestamp
                  });
                }
              } else if (message.includes('🔄 Fixed') && message.includes('AI jargon')) {
                const jargonMatch = message.match(/Fixed (\d+) instances of AI jargon/);
                if (jargonMatch) {
                  sendEvent('humanization', {
                    type: 'jargon_removal',
                    count: parseInt(jargonMatch[1]),
                    message: `Removed ${jargonMatch[1]} instances of AI jargon`,
                    timestamp
                  });
                }
              } else if (message.includes('✅ Applied advanced AI detection bypass')) {
                const improvementsMatch = message.match(/bypass techniques: (.+)$/);
                if (improvementsMatch) {
                  const improvements = improvementsMatch[1].split(', ');
                  sendEvent('humanization', {
                    type: 'bypass_complete',
                    improvements,
                    message: 'AI detection bypass techniques applied',
                    timestamp
                  });
                }
              } else if (message.includes('📊 Final word count:')) {
                const wordMatch = message.match(/Final word count: (\d+)/);
                if (wordMatch) {
                  sendEvent('progress', {
                    wordCount: parseInt(wordMatch[1]),
                    message: `Generated ${wordMatch[1]} words`,
                    timestamp
                  });
                }
              } else if (message.includes('✅ Enhanced Invincible agent completed')) {
                const timeMatch = message.match(/completed in (\d+)ms/);
                sendEvent('progress', {
                  message: 'Generation process completed!',
                  executionTime: timeMatch ? parseInt(timeMatch[1]) : null,
                  timestamp
                });
              }
            };

            const context = {
              taskId: `stream_${Date.now()}`,
              topic,
              contentLength: contentLength ? parseInt(contentLength) : 2000,
              tone: tone || 'professional',
              targetAudience: targetAudience || 'general audience',
              customInstructions: customInstructions || undefined
            };

            sendEvent('context', {
              message: 'Starting content generation...',
              context,
              timestamp: Date.now()
            });

            // Execute the agent
            const result = await agent.execute(context);

            if (result.success && result.article) {
              sendEvent('success', {
                message: 'Content generation completed successfully!',
                article: {
                  title: result.article.title,
                  content: result.article.content,
                  metaDescription: result.article.metaDescription,
                  wordCount: result.article.wordCount,
                  seoScore: result.article.seoScore,
                  readabilityScore: result.article.readabilityScore
                },
                stats: {
                  executionTime: result.executionTime,
                  uniquenessScore: result.uniquenessScore,
                  totalSources: result.researchData?.totalSources || 0
                },
                factCheckReport: result.factCheckReport,
                timestamp: Date.now()
              });
            } else {
              sendEvent('error', {
                message: 'Content generation failed',
                error: result.error,
                timestamp: Date.now()
              });
            }

            sendEvent('complete', {
              message: 'Stream completed',
              timestamp: Date.now()
            });

            // Clean up heartbeat
            clearInterval(heartbeatInterval);
            controller.close();

          } catch (error) {
            console.error('Streaming error:', error);
            console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace');
            
            try {
              sendEvent('error', {
                message: 'An error occurred during generation',
                error: error instanceof Error ? error.message : 'Unknown error',
                errorType: error instanceof Error ? error.constructor.name : 'Unknown',
                timestamp: Date.now()
              });
              
              sendEvent('complete', {
                message: 'Stream completed with error',
                timestamp: Date.now()
              });
            } catch (sendError) {
              console.error('Failed to send error event:', sendError);
            }
            
            // Clean up heartbeat and ensure controller is closed
            clearInterval(heartbeatInterval);
            try {
              controller.close();
            } catch (closeError) {
              console.error('Failed to close controller:', closeError);
            }
          }
        })();
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });

  } catch (error) {
    console.error('SSE setup error:', error);
    return NextResponse.json(
      { error: 'Failed to setup streaming response' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
} 