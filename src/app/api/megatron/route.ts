import { NextRequest, NextResponse } from 'next/server';

// GET endpoint to check Megatron agent status
export async function GET(req: NextRequest) {
  return NextResponse.json({
    agent: 'Megatron',
    version: '1.0.0',
    status: 'ready',
    description: 'YouTube Video Analyzer and Content Generator',
    capabilities: [
      'YouTube caption extraction',
      'Multi-language support',
      'Single vs multiple topic detection',
      'AI-powered content analysis',
      'Topic summarization',
      'Integration with Invincible for article generation',
      'Integration with YouTube Scripts for script generation'
    ],
    workflow: {
      steps: 4,
      description: 'Extract captions → Analyze with Gemini → Generate topic cards → Route to content generation'
    },
    features: [
      'Advanced YouTube URL validation',
      'Real-time caption extraction',
      'Intelligent topic identification',
      'Interactive content cards',
      'Seamless integration with existing agents',
      'Responsive UI with animations'
    ],
    integrations: {
      youtube: 'YouTubeService for caption extraction',
      ai: 'Gemini 2.5 Flash for content analysis',
      invincible: 'Direct routing for article generation',
      scripts: 'Direct routing for script generation'
    }
  });
}
